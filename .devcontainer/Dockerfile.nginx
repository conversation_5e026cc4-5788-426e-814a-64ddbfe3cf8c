FROM alpine/openssl

WORKDIR /root
RUN openssl req -new -newkey rsa:4096 -days 365 -nodes -x509 \
    -subj "/C=BE/ST=Flanders/L=Lochristi/O=Engineering/CN=iam.cairo-cloud.eg.local" \
    -addext "subjectAltName = DNS:iam.cairo-cloud.eg.local" \
    -keyout iam.cairo-cloud.eg.local.key  -out iam.cairo-cloud.eg.local.cert
RUN openssl req -new -newkey rsa:4096 -days 365 -nodes -x509 \
    -subj "/C=BE/ST=Flanders/L=Lochristi/O=Engineering/CN=cairo-cloud.eg.local" \
    -addext "subjectAltName = DNS:cairo-cloud.eg.local" \
    -keyout cairo-cloud.eg.local.key  -out cairo-cloud.eg.local.cert

FROM nginx
COPY nginx.conf /etc/nginx/nginx.conf
COPY --from=0 /root/iam.cairo-cloud.eg.local.key /etc/nginx/
COPY --from=0 /root/iam.cairo-cloud.eg.local.cert /etc/nginx/
COPY --from=0 /root/cairo-cloud.eg.local.key /etc/nginx/
COPY --from=0 /root/cairo-cloud.eg.local.cert /etc/nginx/
RUN mkdir -p /var/certs && cp /etc/nginx/cairo-cloud.eg.local.cert /var/certs && cp /etc/nginx/iam.cairo-cloud.eg.local.cert /var/certs
EXPOSE 443
EXPOSE 80