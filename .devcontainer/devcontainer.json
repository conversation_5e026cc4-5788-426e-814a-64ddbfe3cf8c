// For format details, see https://aka.ms/devcontainer.json. For config options, see the README at:
// https://github.com/microsoft/vscode-dev-containers/tree/v0.140.1/containers/python-3
{
	"name": "<PERSON><PERSON><PERSON>",
	"dockerComposeFile": "docker-compose.yml",
	"service": "mnj-app",
	"workspaceFolder": "/workspace",
	// Set *default* container specific settings.json values on container create.
	"settings": {
		"terminal.integrated.shell.linux": "/bin/bash",
		"python.pythonPath": "/usr/local/bin/python",
		"python.linting.enabled": true,
		"python.linting.pylintEnabled": true,
		"python.formatting.autopep8Path": "/usr/local/py-utils/bin/autopep8",
		"python.formatting.blackPath": "/usr/local/py-utils/bin/black",
		"python.linting.banditPath": "/usr/local/py-utils/bin/bandit",
		"python.linting.flake8Path": "/usr/local/py-utils/bin/flake8",
		"python.linting.mypyPath": "/usr/local/py-utils/bin/mypy",
		"python.linting.pycodestylePath": "/usr/local/py-utils/bin/pycodestyle",
		"python.linting.pydocstylePath": "/usr/local/py-utils/bin/pydocstyle",
		"python.linting.pylintPath": "/usr/local/py-utils/bin/pylint",
		"python.linting.pylintUseMinimalCheckers": false,
		"python.linting.pylintArgs": [
			"--max-line-length=120"
		],
	},
	// Add the IDs of extensions you want installed when the container is created.
	"extensions": [
		"ms-python.python",
		"ms-python.black-formatter",
		"ms-python.vscode-pylance",
		"ms-python.isort",
		"mongodb.mongodb-vscode",
		"njpwerner.autodocstring",
		"wholroyd.jinja",
		"waderyan.gitblame",
		"streetsidesoftware.code-spell-checker",
		"gruntfuggly.todo-tree",
	],
	"mounts": [
		"source=/var/run/docker.sock,target=/var/run/docker.sock,type=bind"
	],
	// Use 'forwardPorts' to make a list of ports inside the container available locally.
	// "forwardPorts": [80, 9000],
	// Use 'postCreateCommand' to run commands after the container is created.
	"postCreateCommand": "sudo curl -o /usr/local/share/ca-certificates/iam.cairo-cloud.eg.local.crt http://iam.cairo-cloud.eg.local/iam.cairo-cloud.eg.local.cert && sudo update-ca-certificates && cd /workspace/scripts/mongodb_bootstrap && bash bootstrap.sh",
	// Uncomment to connect as a non-root user. See https://aka.ms/vscode-remote/containers/non-root.
	"remoteUser": "vscode"
}