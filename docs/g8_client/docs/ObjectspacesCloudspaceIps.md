# ObjectspacesCloudspaceIps

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**network_id** | **int** | Network id of the cloudspace used as vxlan in the objectspace | 
**address** | **str** | name of objectspace | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


