# CloudspacesWireGuardPeerStruct

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**name** | **str** | peer name | 
**public_key** | **str** | peer public key | 
**endpoint** | **str** | peer endpoint | [optional] 
**allowed_ips** | [**list[CloudspacesWireGuardAllowedIPStruct]**](CloudspacesWireGuardAllowedIPStruct.md) | allowed ips | 
**keep_alive** | **int** | optional max keep alive time in seconds | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


