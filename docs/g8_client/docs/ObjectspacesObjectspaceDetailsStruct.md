# ObjectspacesObjectspaceDetailsStruct

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **int** | id of objectspace | 
**name** | **str** | name of objectspace | 
**status** | **str** | status of objectspace | 
**creation_time** | **int** | creation timestamp of the objectspace | 
**update_time** | **int** | update timestamp of the objectspace | 
**account** | [**ObjectspacesObjectspaceDetailsStructAccount**](ObjectspacesObjectspaceDetailsStructAccount.md) |  | 
**access_key** | **str** | access key of objectspace | 
**secret_key** | **str** | secret key of objectspace | 
**prefix** | **str** | prefix for the namespaces in objectspace | 
**audits** | [**list[ObjectspacesAuditInfo]**](ObjectspacesAuditInfo.md) | Audit list associated with the objectspace | 
**buckets** | [**list[ObjectspacesBucketInfo]**](ObjectspacesBucketInfo.md) | connected buckets to objectspace | 
**user_access** | [**list[ObjectspacesACEUserStruct]**](ObjectspacesACEUserStruct.md) | List containing all user accesses | 
**group_access** | [**list[ObjectspacesACEGroupStruct]**](ObjectspacesACEGroupStruct.md) | List containing all group accesses | 
**connected_cloudspaces** | [**list[ObjectspacesCloudspaceInfo]**](ObjectspacesCloudspaceInfo.md) | connected cloudspaces to this objectspace | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


