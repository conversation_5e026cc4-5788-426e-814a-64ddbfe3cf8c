# VirtualGpu

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**account_id** | **int** | Account id | [optional] 
**acl** | [**list[VirtualGpuACE]**](VirtualGpuACE.md) | Access control list | [optional] 
**creation_time** | **int** | Epoch for creation time | [optional] 
**deletion_time** | **int** | Epoch for deletion time | [optional] 
**device_xml** | **str** | Device xml | [optional] 
**guid** | **str** | UUID to attach to vm | [optional] 
**name** | **str** | Virtual gpu name givin by teh user | [optional] 
**status** | **str** | VGPU Status | [optional] 
**update_time** | **int** | Epoch for update time | [optional] 
**id** | **str** | VGPU id | [optional] 
**gpu_id** | **str** | GPU id | [optional] 
**description** | **str** | VGPU description | [optional] 
**subdevices_count** | **int** | Number of subdevices of the gpu | [optional] 
**virtual_machine** | [**VirtualGpuVirtualMachine**](VirtualGpuVirtualMachine.md) |  | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


