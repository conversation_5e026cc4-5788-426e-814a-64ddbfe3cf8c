# VmachinesCreateVMFromBackupStructCREATE

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**target_id** | **int** | backup target id | 
**backup_id** | **str** | backup id | 
**cloudspace_id** | **int** | cloudspace id | 
**name** | **str** | machine name | [optional] 
**description** | **str** | machine description | [optional] 
**hostname** | **str** | machine hostname | [optional] 
**private_ip** | **str** | machine private ip | [optional] 
**tpm_secret** | **str** | tpm secret | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


