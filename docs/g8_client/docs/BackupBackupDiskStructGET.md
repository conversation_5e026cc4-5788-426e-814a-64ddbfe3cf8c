# BackupBackupDiskStructGET

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **int** | default | 
**size** | **int** | default | 
**type** | **str** | default | 
**reference_id** | **str** | default | 
**host** | **str** | default | [optional] 
**port** | **int** | default | [optional] 
**backup_size** | **int** | default | [optional] 
**delta_size** | **int** | default | [optional] 
**restic_snapshot_id** | **str** | default | [optional] 
**restic_snapshot_path** | **str** | default | [optional] 
**snapshot_guid** | **str** | default | [optional] 
**clone_disk_guid** | **str** | default | [optional] 
**backup_blocksize** | **int** | default | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


