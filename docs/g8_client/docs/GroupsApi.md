# meneja.lib.clients.g8.lib.GroupsApi

All URIs are relative to *https://localhost/api/1*

Method | HTTP request | Description
------------- | ------------- | -------------
[**delete_g8_group_metadata_key_value**](GroupsApi.md#delete_g8_group_metadata_key_value) | **DELETE** /groups/{group_name}/metadata/{key} | Delete metadata key
[**get_g8_group_metadata_key_value**](GroupsApi.md#get_g8_group_metadata_key_value) | **GET** /groups/{group_name}/metadata/{key} | Get metadata value
[**get_group_metadata_settings**](GroupsApi.md#get_group_metadata_settings) | **GET** /groups/{group_name}/settings | Get Settings
[**get_list_group_metadata**](GroupsApi.md#get_list_group_metadata) | **GET** /groups/{group_name}/metadata | List Groups Metadata
[**set_g8_group_metadata_key_value**](GroupsApi.md#set_g8_group_metadata_key_value) | **POST** /groups/{group_name}/metadata/{key} | Set metadata value
[**set_group_metadata_settings**](GroupsApi.md#set_group_metadata_settings) | **PUT** /groups/{group_name}/settings | Set Settings


# **delete_g8_group_metadata_key_value**
> delete_g8_group_metadata_key_value(group_name, key)

Delete metadata key

Delete a specific key-value pair in group metadata

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.GroupsApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
group_name = 'group_name_example' # str | 
key = 'key_example' # str | 

try:
    # Delete metadata key
    api_instance.delete_g8_group_metadata_key_value(group_name, key)
except ApiException as e:
    print("Exception when calling GroupsApi->delete_g8_group_metadata_key_value: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **group_name** | **str**|  | 
 **key** | **str**|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_g8_group_metadata_key_value**
> get_g8_group_metadata_key_value(group_name, key)

Get metadata value

Get a specific key-value pair from group metadata

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.GroupsApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
group_name = 'group_name_example' # str | 
key = 'key_example' # str | 

try:
    # Get metadata value
    api_instance.get_g8_group_metadata_key_value(group_name, key)
except ApiException as e:
    print("Exception when calling GroupsApi->get_g8_group_metadata_key_value: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **group_name** | **str**|  | 
 **key** | **str**|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_group_metadata_settings**
> RuntimeSettings get_group_metadata_settings(group_name, x_fields=x_fields)

Get Settings

Get group metadata settings

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.GroupsApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
group_name = 'group_name_example' # str | 
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # Get Settings
    api_response = api_instance.get_group_metadata_settings(group_name, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling GroupsApi->get_group_metadata_settings: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **group_name** | **str**|  | 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**RuntimeSettings**](RuntimeSettings.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_list_group_metadata**
> GroupMetadata get_list_group_metadata(group_name, x_fields=x_fields)

List Groups Metadata

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.GroupsApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
group_name = 'group_name_example' # str | 
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # List Groups Metadata
    api_response = api_instance.get_list_group_metadata(group_name, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling GroupsApi->get_list_group_metadata: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **group_name** | **str**|  | 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**GroupMetadata**](GroupMetadata.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **set_g8_group_metadata_key_value**
> set_g8_group_metadata_key_value(group_name, key, payload)

Set metadata value

Set or update a specific key-value pair in group metadata

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.GroupsApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
group_name = 'group_name_example' # str | 
key = 'key_example' # str | 
payload = meneja.lib.clients.g8.lib.MetadataKeyValue() # MetadataKeyValue | 

try:
    # Set metadata value
    api_instance.set_g8_group_metadata_key_value(group_name, key, payload)
except ApiException as e:
    print("Exception when calling GroupsApi->set_g8_group_metadata_key_value: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **group_name** | **str**|  | 
 **key** | **str**|  | 
 **payload** | [**MetadataKeyValue**](MetadataKeyValue.md)|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **set_group_metadata_settings**
> set_group_metadata_settings(group_name, payload)

Set Settings

Set group metadata settings

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.GroupsApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
group_name = 'group_name_example' # str | 
payload = meneja.lib.clients.g8.lib.RuntimeSettings() # RuntimeSettings | 

try:
    # Set Settings
    api_instance.set_group_metadata_settings(group_name, payload)
except ApiException as e:
    print("Exception when calling GroupsApi->set_group_metadata_settings: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **group_name** | **str**|  | 
 **payload** | [**RuntimeSettings**](RuntimeSettings.md)|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

