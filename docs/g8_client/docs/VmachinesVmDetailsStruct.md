# VmachinesVmDetailsStruct

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **int** | vm id | 
**name** | **str** | vm name | 
**description** | **str** | vm description | 
**status** | **str** | vm status | 
**image** | [**VmachinesVmDetailsStructImage**](VmachinesVmDetailsStructImage.md) |  | 
**memory** | **int** | vm memory | 
**cloudspace** | [**VmachinesVmDetailsStructCloudspace**](VmachinesVmDetailsStructCloudspace.md) |  | 
**creation_time** | **int** | creation timestamp | 
**update_time** | **int** | update timestamp | 
**vcpus** | **int** | vcpus | 
**hypervisor_type** | **str** | vcpus | 
**tags** | [**list[VmachinesTagStruct]**](VmachinesTagStruct.md) | tags | 
**disks** | [**list[VmachinesDiskStruct]**](VmachinesDiskStruct.md) | tags | 
**audits** | [**list[Audit]**](Audit.md) | list of audits for this vm | 
**user_access** | [**list[UserAccessStruct]**](UserAccessStruct.md) | User access rights | 
**group_access** | [**list[GroupAccessStruct]**](GroupAccessStruct.md) | Group access rights | 
**nics** | [**list[VmachinesNicInfo]**](VmachinesNicInfo.md) | list of nics for this vm | 
**node** | [**VmachinesVmDetailsStructNode**](VmachinesVmDetailsStructNode.md) |  | 
**account** | [**VmachinesVmDetailsStructAccount**](VmachinesVmDetailsStructAccount.md) |  | 
**devices** | [**list[VmachinesVmachineDeviceStruct]**](VmachinesVmachineDeviceStruct.md) | Devices list | 
**location_history** | [**list[VmachinesVmachineLoacaionHistoryStruct]**](VmachinesVmachineLoacaionHistoryStruct.md) | Location history | 
**backup_policies** | [**list[VmachinesVMBackupPolicy]**](VmachinesVMBackupPolicy.md) | Backup policies | 
**backup_status** | **str** | latest backup status | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


