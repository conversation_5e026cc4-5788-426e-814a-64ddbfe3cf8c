# ExternalNetworksUsageResourceStruct

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **int** | id of resource consuming IPAddresses | 
**account_id** | **int** | id of account owning the resource consuming IPAddresses | 
**name** | **str** | Name of resource | 
**type** | **str** | Type of resource | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


