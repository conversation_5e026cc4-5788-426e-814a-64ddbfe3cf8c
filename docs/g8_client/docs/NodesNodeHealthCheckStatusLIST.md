# NodesNodeHealthCheckStatusLIST

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**grid_id** | **int** | Grid id for the node | 
**node_id** | **int** | Node id | 
**node_name** | **str** | Node name | 
**status** | **str** | Worst health check status at the node | 
**muted_count** | **int** | Number of muted health check at the node | 
**faulty_categories** | **list[str]** | List of categories that has faulty status | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


