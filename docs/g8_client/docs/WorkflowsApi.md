# meneja.lib.clients.g8.lib.WorkflowsApi

All URIs are relative to *https://localhost/api/1*

Method | HTTP request | Description
------------- | ------------- | -------------
[**get_g8_task_logs**](WorkflowsApi.md#get_g8_task_logs) | **GET** /workflows/{workflow_id}/task/{task_id}/logs | Lists the logs of a task
[**get_g8_workflow**](WorkflowsApi.md#get_g8_workflow) | **GET** /workflows/{workflow_id} | Get workflow by id
[**get_g8_workflow_logs**](WorkflowsApi.md#get_g8_workflow_logs) | **GET** /workflows/{workflow_id}/logs | List the logs of a workflow
[**list_g8_workflows**](WorkflowsApi.md#list_g8_workflows) | **GET** /workflows | List workflows


# **get_g8_task_logs**
> list[LogLine] get_g8_task_logs(workflow_id, task_id, include_debug_logs=include_debug_logs, x_fields=x_fields)

Lists the logs of a task

Lists the logs of a task

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.WorkflowsApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
workflow_id = 'workflow_id_example' # str | 
task_id = 'task_id_example' # str | 
include_debug_logs = true # bool | Flag to whether to include debug log records.  (optional) (default to true)
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # Lists the logs of a task
    api_response = api_instance.get_g8_task_logs(workflow_id, task_id, include_debug_logs=include_debug_logs, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling WorkflowsApi->get_g8_task_logs: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **workflow_id** | **str**|  | 
 **task_id** | **str**|  | 
 **include_debug_logs** | **bool**| Flag to whether to include debug log records.  | [optional] [default to true]
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**list[LogLine]**](LogLine.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_g8_workflow**
> WorkflowsWorkflowStep get_g8_workflow(workflow_id, include_logs=include_logs, include_debug_logs=include_debug_logs, x_fields=x_fields)

Get workflow by id

Gets details of a specific workflow

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.WorkflowsApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
workflow_id = 'workflow_id_example' # str | 
include_logs = true # bool | Flag to whether to include logs in the result set (optional) (default to true)
include_debug_logs = true # bool | Flag to whether to include debug log records. This parameter is only relevant if include_logs is True (optional) (default to true)
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # Get workflow by id
    api_response = api_instance.get_g8_workflow(workflow_id, include_logs=include_logs, include_debug_logs=include_debug_logs, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling WorkflowsApi->get_g8_workflow: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **workflow_id** | **str**|  | 
 **include_logs** | **bool**| Flag to whether to include logs in the result set | [optional] [default to true]
 **include_debug_logs** | **bool**| Flag to whether to include debug log records. This parameter is only relevant if include_logs is True | [optional] [default to true]
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**WorkflowsWorkflowStep**](WorkflowsWorkflowStep.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_g8_workflow_logs**
> list[LogLine] get_g8_workflow_logs(workflow_id, include_debug_logs=include_debug_logs, x_fields=x_fields)

List the logs of a workflow

Lists the logs of a workflow

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.WorkflowsApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
workflow_id = 'workflow_id_example' # str | 
include_debug_logs = true # bool | Flag to whether to include debug log records.  (optional) (default to true)
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # List the logs of a workflow
    api_response = api_instance.get_g8_workflow_logs(workflow_id, include_debug_logs=include_debug_logs, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling WorkflowsApi->get_g8_workflow_logs: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **workflow_id** | **str**|  | 
 **include_debug_logs** | **bool**| Flag to whether to include debug log records.  | [optional] [default to true]
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**list[LogLine]**](LogLine.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **list_g8_workflows**
> WorkflowsPagination list_g8_workflows(limit=limit, start_after=start_after, sort_by=sort_by, sort_direction=sort_direction, title=title, status=status, x_fields=x_fields)

List workflows

Lists workflows running in the G8

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.WorkflowsApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
limit = 25 # int | Flag to limit the amount of results. (optional) (default to 25)
start_after = 'start_after_example' # str | Start returning records after index (optional)
sort_by = 'sort_by_example' # str | sorting field (optional)
sort_direction = 1 # int | sorting direction. 1 for asc and -1 for desc (optional) (default to 1)
title = 'title_example' # str | Workflows title filter (optional)
status = 'status_example' # str | Workflows status filter (optional)
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # List workflows
    api_response = api_instance.list_g8_workflows(limit=limit, start_after=start_after, sort_by=sort_by, sort_direction=sort_direction, title=title, status=status, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling WorkflowsApi->list_g8_workflows: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **limit** | **int**| Flag to limit the amount of results. | [optional] [default to 25]
 **start_after** | **str**| Start returning records after index | [optional] 
 **sort_by** | **str**| sorting field | [optional] 
 **sort_direction** | **int**| sorting direction. 1 for asc and -1 for desc | [optional] [default to 1]
 **title** | **str**| Workflows title filter | [optional] 
 **status** | **str**| Workflows status filter | [optional] 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**WorkflowsPagination**](WorkflowsPagination.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

