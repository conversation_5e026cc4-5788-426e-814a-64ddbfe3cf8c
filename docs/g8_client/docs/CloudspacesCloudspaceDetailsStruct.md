# CloudspacesCloudspaceDetailsStruct

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **int** | Cloudspace unique id | 
**name** | **str** | Cloudspace name | 
**account** | [**CloudspacesCloudspaceDetailsStructAccount**](CloudspacesCloudspaceDetailsStructAccount.md) |  | 
**networks** | [**list[CloudspacesNetworkStruct]**](CloudspacesNetworkStruct.md) | Cloudspace networks info | 
**status** | **str** | Cloudspace status | 
**creation_time** | **int** | Epoch for cloudspace creation timestamp | 
**mode** | **str** | Mode for the cloudspace, public/nested | 
**update_time** | **int** | Epoch for cloudspace update timestamp | 
**external_networks** | [**list[ExternalNetworkDetailsStruct]**](ExternalNetworkDetailsStruct.md) | Cloudspace extrenal networks | 
**limits** | [**CloudspacesCloudspaceDetailsStructLimits**](CloudspacesCloudspaceDetailsStructLimits.md) |  | 
**user_access** | [**list[UserAccessStruct]**](UserAccessStruct.md) | User access rights | 
**group_access** | [**list[GroupAccessStruct]**](GroupAccessStruct.md) | Group access rights | 
**virtual_machines** | [**list[CloudspacesVirtualMachineStruct]**](CloudspacesVirtualMachineStruct.md) | List of virtual machines on the cloudspace | 
**ipsec_tunnels** | [**list[CloudspacesIpsecTunnelStruct]**](CloudspacesIpsecTunnelStruct.md) | Ipsec connections information | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


