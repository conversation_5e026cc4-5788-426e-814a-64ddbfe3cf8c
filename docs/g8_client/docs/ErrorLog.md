# ErrorLog

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**last_occurrence_time** | **int** | Timestamp of last occurrence | [optional] 
**occurred** | **int** | Number of times this particular error occurred | [optional] 
**occurrences** | [**list[ErrorLogErrorInstance]**](ErrorLogErrorInstance.md) | Last 10 occurrences of this particular error | [optional] 
**traceback** | **str** | Stack trace of uncaught error | [optional] 
**traceback_hash** | **str** | md5sum of the traceback | [optional] 
**message** | **str** | error msg | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


