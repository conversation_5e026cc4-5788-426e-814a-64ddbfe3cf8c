# VgpusGPUStruct

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**pci_address** | **str** | PCI address | 
**model** | **str** | GPU model | 
**vendor** | **str** | GPU vendor | 
**vgpu_types** | [**list[VgpusVGPUTypeStruct]**](VgpusVGPUTypeStruct.md) | List of VGPU types | 
**id** | **str** | GPU id for enabled gpus only | [optional] 
**node_id** | **int** | Node id | [optional] 
**node_name** | **str** | Node name | [optional] 
**vgpu_multiplier** | **float** | Vgpu multiplier | [optional] 
**description** | **str** | Description | [optional] [default to '']

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


