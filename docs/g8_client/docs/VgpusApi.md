# meneja.lib.clients.g8.lib.VgpusApi

All URIs are relative to *https://localhost/api/1*

Method | HTTP request | Description
------------- | ------------- | -------------
[**g8_create_vgpu**](VgpusApi.md#g8_create_vgpu) | **POST** /vgpus | Create VGPU
[**g8_list_gpu_non_admin**](VgpusApi.md#g8_list_gpu_non_admin) | **GET** /vgpus/available | List enabled GPUs for non-admin users
[**g8_list_vgpu**](VgpusApi.md#g8_list_vgpu) | **GET** /vgpus | List VGPU
[**g8_remove_vgpu**](VgpusApi.md#g8_remove_vgpu) | **DELETE** /vgpus/{vgpu_guid} | Remove GPU
[**g8_restore_vgpu**](VgpusApi.md#g8_restore_vgpu) | **POST** /vgpus/{vgpu_guid} | Restore VGPU


# **g8_create_vgpu**
> VirtualGpu g8_create_vgpu(payload, x_fields=x_fields)

Create VGPU

Create VGPU

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.VgpusApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
payload = meneja.lib.clients.g8.lib.VgpusVgpuCreateStruct() # VgpusVgpuCreateStruct | 
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # Create VGPU
    api_response = api_instance.g8_create_vgpu(payload, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling VgpusApi->g8_create_vgpu: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **payload** | [**VgpusVgpuCreateStruct**](VgpusVgpuCreateStruct.md)|  | 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**VirtualGpu**](VirtualGpu.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g8_list_gpu_non_admin**
> GPUShortStructs g8_list_gpu_non_admin(x_fields=x_fields)

List enabled GPUs for non-admin users

List enabled GPUs for non-admin users

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.VgpusApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # List enabled GPUs for non-admin users
    api_response = api_instance.g8_list_gpu_non_admin(x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling VgpusApi->g8_list_gpu_non_admin: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**GPUShortStructs**](GPUShortStructs.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g8_list_vgpu**
> VirtualGPUPagination g8_list_vgpu(limit=limit, start_after=start_after, sort_by=sort_by, sort_direction=sort_direction, status=status, account_id=account_id, guid=guid, x_fields=x_fields)

List VGPU

List VGPU

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.VgpusApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
limit = 25 # int | Flag to limit the amount of results. (optional) (default to 25)
start_after = 56 # int | Start returning records after index (optional)
sort_by = 'sort_by_example' # str | sorting field (optional)
sort_direction = 1 # int | sorting direction. 1 for asc and -1 for desc (optional) (default to 1)
status = ['status_example'] # list[str] | search status separated by a comma (optional)
account_id = 56 # int | search vgpus by account id (optional)
guid = 'guid_example' # str | search vgpus by guid (optional)
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # List VGPU
    api_response = api_instance.g8_list_vgpu(limit=limit, start_after=start_after, sort_by=sort_by, sort_direction=sort_direction, status=status, account_id=account_id, guid=guid, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling VgpusApi->g8_list_vgpu: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **limit** | **int**| Flag to limit the amount of results. | [optional] [default to 25]
 **start_after** | **int**| Start returning records after index | [optional] 
 **sort_by** | **str**| sorting field | [optional] 
 **sort_direction** | **int**| sorting direction. 1 for asc and -1 for desc | [optional] [default to 1]
 **status** | [**list[str]**](str.md)| search status separated by a comma | [optional] 
 **account_id** | **int**| search vgpus by account id | [optional] 
 **guid** | **str**| search vgpus by guid | [optional] 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**VirtualGPUPagination**](VirtualGPUPagination.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g8_remove_vgpu**
> g8_remove_vgpu(vgpu_guid, payload)

Remove GPU

Remove VGPU

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.VgpusApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
vgpu_guid = 'vgpu_guid_example' # str | 
payload = meneja.lib.clients.g8.lib.DeleteInputStruct() # DeleteInputStruct | 

try:
    # Remove GPU
    api_instance.g8_remove_vgpu(vgpu_guid, payload)
except ApiException as e:
    print("Exception when calling VgpusApi->g8_remove_vgpu: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **vgpu_guid** | **str**|  | 
 **payload** | [**DeleteInputStruct**](DeleteInputStruct.md)|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g8_restore_vgpu**
> g8_restore_vgpu(vgpu_guid)

Restore VGPU

Restore VGPU

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.VgpusApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
vgpu_guid = 'vgpu_guid_example' # str | 

try:
    # Restore VGPU
    api_instance.g8_restore_vgpu(vgpu_guid)
except ApiException as e:
    print("Exception when calling VgpusApi->g8_restore_vgpu: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **vgpu_guid** | **str**|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

