# HealthCheck

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**dynaqueue** | **bool** | Switch that defines whether the task was executed in jumpscale or dynaqueue. | [optional] 
**id** | **str** | None | [optional] 
**interval** | **int** | Time for how often the health check is run (cron time) | [optional] 
**jobguid** | **str** | Dynaqueue task id that executed the health check | [optional] 
**last_checked_timestamp** | **int** | Epoch of when the last time the health check is executed | [optional] 
**messages** | [**list[HealthCheckMessage]**](HealthCheckMessage.md) | A list of health check result | [optional] 
**name** | **str** | Unique string for the health check, currently is the module name | [optional] 
**node_id** | **int** | Node id where the health check run | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


