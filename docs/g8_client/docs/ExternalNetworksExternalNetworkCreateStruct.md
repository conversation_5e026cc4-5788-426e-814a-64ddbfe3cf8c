# ExternalNetworksExternalNetworkCreateStruct

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**name** | **str** | External network name | 
**account_id** | **int** | account id which can use this network. empty list means it is a public external network. kept for backwards compitablity and to be removed | [optional] 
**billable** | **bool** | Indicates if billing records need to be gathered for the use | 
**dhcp** | **bool** | Indicates if a dhcp server automatically hands out ip addresses | 
**gateway** | **str** | Gateway to the upstream network | 
**network** | **str** | Network of the pool | 
**subnet_mask** | **str** | Subnet mask of the pool | 
**ips** | [**list[ExternalNetworksExternalNetworkIpStructLIST]**](ExternalNetworksExternalNetworkIpStructLIST.md) |  | [optional] 
**ping_ips** | [**list[IpShort]**](IpShort.md) | List of ips to be pinged to check the external network health | 
**protection** | **bool** | Indicates if this external network has ip-mac protection or not. | 
**speed_limit** | **int** | Maximum bandwith speed configured on the network interfaces expressed in Kbit. 0 is the default which gets translated into 1 Gbit | 
**sriov** | **bool** | Make use of SR-IOV interfaces for this network | 
**vlan** | **int** | Vlan tag used for isolating this external network | 
**account_ids** | **list[int]** | List of accounts which can use this network. empty list means it is a public external network. | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


