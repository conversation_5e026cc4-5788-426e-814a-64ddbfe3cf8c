# meneja.lib.clients.g8.lib.LocationsApi

All URIs are relative to *https://localhost/api/1*

Method | HTTP request | Description
------------- | ------------- | -------------
[**get_g8_location_settings**](LocationsApi.md#get_g8_location_settings) | **GET** /locations/settings | Get Runtime Settings
[**get_get_system_account**](LocationsApi.md#get_get_system_account) | **GET** /locations/system_account | Get System Account
[**set_g8_location_settings**](LocationsApi.md#set_g8_location_settings) | **PUT** /locations/settings | Set Runtime Settings


# **get_g8_location_settings**
> RuntimeSettings get_g8_location_settings(x_fields=x_fields)

Get Runtime Settings

Get a G8 location settings

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.LocationsApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # Get Runtime Settings
    api_response = api_instance.get_g8_location_settings(x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling LocationsApi->get_g8_location_settings: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**RuntimeSettings**](RuntimeSettings.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_get_system_account**
> AccountsAccountDetailsStruct get_get_system_account(x_fields=x_fields)

Get System Account

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.LocationsApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # Get System Account
    api_response = api_instance.get_get_system_account(x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling LocationsApi->get_get_system_account: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**AccountsAccountDetailsStruct**](AccountsAccountDetailsStruct.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **set_g8_location_settings**
> set_g8_location_settings(payload)

Set Runtime Settings

Set a G8 location settings

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.LocationsApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
payload = meneja.lib.clients.g8.lib.RuntimeSettings() # RuntimeSettings | 

try:
    # Set Runtime Settings
    api_instance.set_g8_location_settings(payload)
except ApiException as e:
    print("Exception when calling LocationsApi->set_g8_location_settings: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **payload** | [**RuntimeSettings**](RuntimeSettings.md)|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

