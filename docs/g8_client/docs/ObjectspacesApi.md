# meneja.lib.clients.g8.lib.ObjectspacesApi

All URIs are relative to *https://localhost/api/1*

Method | HTTP request | Description
------------- | ------------- | -------------
[**add_bucket_life_cycle_rule**](ObjectspacesApi.md#add_bucket_life_cycle_rule) | **POST** /objectspaces/{objectspace_id}/buckets/{bucket_name}/lifecycle-rules | Add bucket lifecycle rule
[**cancel_bucket_multipart_uploads**](ObjectspacesApi.md#cancel_bucket_multipart_uploads) | **DELETE** /objectspaces/{objectspace_id}/buckets/{bucket_name}/multipart-uploads/{key}/{upload_id} | Cancel bucket incompelete multipart uploads
[**create_bucket_access**](ObjectspacesApi.md#create_bucket_access) | **POST** /objectspaces/{objectspace_id}/buckets/{bucket_name}/access-management | Create objectspace bucket access
[**delete_bucket_access**](ObjectspacesApi.md#delete_bucket_access) | **DELETE** /objectspaces/{objectspace_id}/buckets/{bucket_name}/access-management/{name} | Delete bucket access
[**delete_bucket_life_cycle_rule**](ObjectspacesApi.md#delete_bucket_life_cycle_rule) | **DELETE** /objectspaces/{objectspace_id}/buckets/{bucket_name}/lifecycle-rules/{rule_id} | Delete bucket lifecycle rule
[**get_g8_objectspace_by_id**](ObjectspacesApi.md#get_g8_objectspace_by_id) | **GET** /objectspaces/{objectspace_id} | Get objectspace by id
[**list_bucket_access**](ObjectspacesApi.md#list_bucket_access) | **GET** /objectspaces/{objectspace_id}/buckets/{bucket_name}/access-management | List objectspace bucket access
[**list_bucket_life_cycle_rule**](ObjectspacesApi.md#list_bucket_life_cycle_rule) | **GET** /objectspaces/{objectspace_id}/buckets/{bucket_name}/lifecycle-rules | List bucket lifecycle rules
[**list_bucket_multipart_uploads**](ObjectspacesApi.md#list_bucket_multipart_uploads) | **GET** /objectspaces/{objectspace_id}/buckets/{bucket_name}/multipart-uploads | List bucket incomplete multipart uploads
[**list_g8_objectspaces**](ObjectspacesApi.md#list_g8_objectspaces) | **GET** /objectspaces | List Objectspaces
[**update_bucket_access**](ObjectspacesApi.md#update_bucket_access) | **PUT** /objectspaces/{objectspace_id}/buckets/{bucket_name}/access-management/{name} | Update bucket access type


# **add_bucket_life_cycle_rule**
> add_bucket_life_cycle_rule(objectspace_id, bucket_name, payload)

Add bucket lifecycle rule

Add bucket lifecycle rule

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.ObjectspacesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
objectspace_id = 56 # int | 
bucket_name = 'bucket_name_example' # str | 
payload = meneja.lib.clients.g8.lib.ObjectspacesBucketLifeCycleRule() # ObjectspacesBucketLifeCycleRule | 

try:
    # Add bucket lifecycle rule
    api_instance.add_bucket_life_cycle_rule(objectspace_id, bucket_name, payload)
except ApiException as e:
    print("Exception when calling ObjectspacesApi->add_bucket_life_cycle_rule: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **objectspace_id** | **int**|  | 
 **bucket_name** | **str**|  | 
 **payload** | [**ObjectspacesBucketLifeCycleRule**](ObjectspacesBucketLifeCycleRule.md)|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **cancel_bucket_multipart_uploads**
> cancel_bucket_multipart_uploads(objectspace_id, bucket_name, key, upload_id)

Cancel bucket incompelete multipart uploads

Cancel bucket incompelete multipart uploads

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.ObjectspacesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
objectspace_id = 56 # int | 
bucket_name = 'bucket_name_example' # str | 
key = 'key_example' # str | 
upload_id = 'upload_id_example' # str | 

try:
    # Cancel bucket incompelete multipart uploads
    api_instance.cancel_bucket_multipart_uploads(objectspace_id, bucket_name, key, upload_id)
except ApiException as e:
    print("Exception when calling ObjectspacesApi->cancel_bucket_multipart_uploads: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **objectspace_id** | **int**|  | 
 **bucket_name** | **str**|  | 
 **key** | **str**|  | 
 **upload_id** | **str**|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **create_bucket_access**
> ObjectspacesObjectSpaceAccessStruct create_bucket_access(objectspace_id, bucket_name, payload, x_fields=x_fields)

Create objectspace bucket access

Create objectspace bucket access

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.ObjectspacesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
objectspace_id = 56 # int | 
bucket_name = 'bucket_name_example' # str | 
payload = meneja.lib.clients.g8.lib.ObjectspacesObjectSpaceAccessRequestStruct() # ObjectspacesObjectSpaceAccessRequestStruct | 
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # Create objectspace bucket access
    api_response = api_instance.create_bucket_access(objectspace_id, bucket_name, payload, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling ObjectspacesApi->create_bucket_access: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **objectspace_id** | **int**|  | 
 **bucket_name** | **str**|  | 
 **payload** | [**ObjectspacesObjectSpaceAccessRequestStruct**](ObjectspacesObjectSpaceAccessRequestStruct.md)|  | 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**ObjectspacesObjectSpaceAccessStruct**](ObjectspacesObjectSpaceAccessStruct.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete_bucket_access**
> delete_bucket_access(objectspace_id, bucket_name, name)

Delete bucket access

Delete bucket access

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.ObjectspacesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
objectspace_id = 56 # int | 
bucket_name = 'bucket_name_example' # str | 
name = 'name_example' # str | 

try:
    # Delete bucket access
    api_instance.delete_bucket_access(objectspace_id, bucket_name, name)
except ApiException as e:
    print("Exception when calling ObjectspacesApi->delete_bucket_access: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **objectspace_id** | **int**|  | 
 **bucket_name** | **str**|  | 
 **name** | **str**|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete_bucket_life_cycle_rule**
> delete_bucket_life_cycle_rule(objectspace_id, bucket_name, rule_id)

Delete bucket lifecycle rule

Delete bucket lifecycle rule

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.ObjectspacesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
objectspace_id = 56 # int | 
bucket_name = 'bucket_name_example' # str | 
rule_id = 'rule_id_example' # str | 

try:
    # Delete bucket lifecycle rule
    api_instance.delete_bucket_life_cycle_rule(objectspace_id, bucket_name, rule_id)
except ApiException as e:
    print("Exception when calling ObjectspacesApi->delete_bucket_life_cycle_rule: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **objectspace_id** | **int**|  | 
 **bucket_name** | **str**|  | 
 **rule_id** | **str**|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_g8_objectspace_by_id**
> ObjectspacesObjectspaceDetailsStruct get_g8_objectspace_by_id(objectspace_id, x_fields=x_fields)

Get objectspace by id

Get details of a specific objectspace

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.ObjectspacesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
objectspace_id = 56 # int | 
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # Get objectspace by id
    api_response = api_instance.get_g8_objectspace_by_id(objectspace_id, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling ObjectspacesApi->get_g8_objectspace_by_id: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **objectspace_id** | **int**|  | 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**ObjectspacesObjectspaceDetailsStruct**](ObjectspacesObjectspaceDetailsStruct.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **list_bucket_access**
> ObjectspacesObjectSpaceAccessListStruct list_bucket_access(objectspace_id, bucket_name, x_fields=x_fields)

List objectspace bucket access

List objectspace bucket access

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.ObjectspacesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
objectspace_id = 56 # int | 
bucket_name = 'bucket_name_example' # str | 
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # List objectspace bucket access
    api_response = api_instance.list_bucket_access(objectspace_id, bucket_name, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling ObjectspacesApi->list_bucket_access: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **objectspace_id** | **int**|  | 
 **bucket_name** | **str**|  | 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**ObjectspacesObjectSpaceAccessListStruct**](ObjectspacesObjectSpaceAccessListStruct.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **list_bucket_life_cycle_rule**
> ObjectspacesBucketLifeCycleRules list_bucket_life_cycle_rule(objectspace_id, bucket_name, x_fields=x_fields)

List bucket lifecycle rules

List bucket lifecycle rules

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.ObjectspacesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
objectspace_id = 56 # int | 
bucket_name = 'bucket_name_example' # str | 
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # List bucket lifecycle rules
    api_response = api_instance.list_bucket_life_cycle_rule(objectspace_id, bucket_name, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling ObjectspacesApi->list_bucket_life_cycle_rule: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **objectspace_id** | **int**|  | 
 **bucket_name** | **str**|  | 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**ObjectspacesBucketLifeCycleRules**](ObjectspacesBucketLifeCycleRules.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **list_bucket_multipart_uploads**
> ObjectspacesBucketMultipartUploads list_bucket_multipart_uploads(objectspace_id, bucket_name, x_fields=x_fields)

List bucket incomplete multipart uploads

List bucket incomplete multipart uploads

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.ObjectspacesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
objectspace_id = 56 # int | 
bucket_name = 'bucket_name_example' # str | 
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # List bucket incomplete multipart uploads
    api_response = api_instance.list_bucket_multipart_uploads(objectspace_id, bucket_name, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling ObjectspacesApi->list_bucket_multipart_uploads: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **objectspace_id** | **int**|  | 
 **bucket_name** | **str**|  | 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**ObjectspacesBucketMultipartUploads**](ObjectspacesBucketMultipartUploads.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **list_g8_objectspaces**
> ObjectspacePagination list_g8_objectspaces(limit=limit, start_after=start_after, sort_by=sort_by, sort_direction=sort_direction, ids=ids, name=name, status=status, account=account, x_fields=x_fields)

List Objectspaces

Lists Objectspaces in the G8

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.ObjectspacesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
limit = 25 # int | Flag to limit the amount of results. (optional) (default to 25)
start_after = 56 # int | Start returning records after index (optional)
sort_by = 'sort_by_example' # str | sorting field (optional)
sort_direction = 1 # int | sorting direction. 1 for asc and -1 for desc (optional) (default to 1)
ids = [56] # list[int] | search ids (optional)
name = 'name_example' # str | search status (optional)
status = 'status_example' # str | search status (optional)
account = 'account_example' # str | search by account name or id (optional)
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # List Objectspaces
    api_response = api_instance.list_g8_objectspaces(limit=limit, start_after=start_after, sort_by=sort_by, sort_direction=sort_direction, ids=ids, name=name, status=status, account=account, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling ObjectspacesApi->list_g8_objectspaces: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **limit** | **int**| Flag to limit the amount of results. | [optional] [default to 25]
 **start_after** | **int**| Start returning records after index | [optional] 
 **sort_by** | **str**| sorting field | [optional] 
 **sort_direction** | **int**| sorting direction. 1 for asc and -1 for desc | [optional] [default to 1]
 **ids** | [**list[int]**](int.md)| search ids | [optional] 
 **name** | **str**| search status | [optional] 
 **status** | **str**| search status | [optional] 
 **account** | **str**| search by account name or id | [optional] 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**ObjectspacePagination**](ObjectspacePagination.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **update_bucket_access**
> update_bucket_access(objectspace_id, bucket_name, name, payload)

Update bucket access type

Update bucket access type

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.ObjectspacesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
objectspace_id = 56 # int | 
bucket_name = 'bucket_name_example' # str | 
name = 'name_example' # str | 
payload = meneja.lib.clients.g8.lib.ObjectspacesUpdateBucketAccessTypeStruct() # ObjectspacesUpdateBucketAccessTypeStruct | 

try:
    # Update bucket access type
    api_instance.update_bucket_access(objectspace_id, bucket_name, name, payload)
except ApiException as e:
    print("Exception when calling ObjectspacesApi->update_bucket_access: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **objectspace_id** | **int**|  | 
 **bucket_name** | **str**|  | 
 **name** | **str**|  | 
 **payload** | [**ObjectspacesUpdateBucketAccessTypeStruct**](ObjectspacesUpdateBucketAccessTypeStruct.md)|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

