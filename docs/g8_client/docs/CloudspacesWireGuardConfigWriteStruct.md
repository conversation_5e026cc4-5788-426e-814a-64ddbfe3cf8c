# CloudspacesWireGuardConfigWriteStruct

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**name** | **str** | interface name | 
**address** | **str** | interface address | 
**port** | **int** | interface listening port | [optional] 
**public_key** | **str** | interface public key | [optional] 
**mtu** | **int** | optional maximum transmission unit | [optional] 
**peers** | [**list[CloudspacesWireGuardPeerStruct]**](CloudspacesWireGuardPeerStruct.md) | peers config | 
**private_key** | **str** | interface private key | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


