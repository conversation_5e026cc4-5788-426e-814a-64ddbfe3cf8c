# meneja.lib.clients.g8.lib.GpusApi

All URIs are relative to *https://localhost/api/1*

Method | HTTP request | Description
------------- | ------------- | -------------
[**g8_disable_gpu**](GpusApi.md#g8_disable_gpu) | **DELETE** /gpus/{gpu_id} | Disable GPU
[**g8_enable_gpu**](GpusApi.md#g8_enable_gpu) | **POST** /gpus | Enable GPU
[**g8_list_gpus**](GpusApi.md#g8_list_gpus) | **GET** /gpus | List all GPUs


# **g8_disable_gpu**
> bool g8_disable_gpu(gpu_id)

Disable GPU

Disable GPU by deleting the recored

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.GpusApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
gpu_id = 'gpu_id_example' # str | 

try:
    # Disable GPU
    api_response = api_instance.g8_disable_gpu(gpu_id)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling GpusApi->g8_disable_gpu: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **gpu_id** | **str**|  | 

### Return type

**bool**

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g8_enable_gpu**
> Gpu g8_enable_gpu(payload, x_fields=x_fields)

Enable GPU

Enable GPU by creating a recored

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.GpusApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
payload = meneja.lib.clients.g8.lib.GpuCreate() # GpuCreate | 
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # Enable GPU
    api_response = api_instance.g8_enable_gpu(payload, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling GpusApi->g8_enable_gpu: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **payload** | [**GpuCreate**](GpuCreate.md)|  | 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**Gpu**](Gpu.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g8_list_gpus**
> GPUStructs g8_list_gpus(x_fields=x_fields)

List all GPUs

List all GPUs

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.GpusApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # List all GPUs
    api_response = api_instance.g8_list_gpus(x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling GpusApi->g8_list_gpus: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**GPUStructs**](GPUStructs.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

