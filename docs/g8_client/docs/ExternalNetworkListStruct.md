# ExternalNetworkListStruct

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **int** | unique ID for the external network | 
**name** | **str** | Name of the external network | 
**type** | **str** | External network type (&#39;external&#39;, &#39;cloudspace&#39;) | 
**subnet** | **str** | Subnet mask for the network | 
**gateway** | **str** | Gateway address | 
**ips** | [**list[IpShort]**](IpShort.md) | List of ip addresses | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


