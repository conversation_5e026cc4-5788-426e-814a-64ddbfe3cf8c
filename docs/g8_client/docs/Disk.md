# Disk

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**account_id** | **int** | Disk owner account id | [optional] 
**acl** | [**list[DiskACE]**](DiskACE.md) | Access control list | [optional] 
**cache** | [**DiskCache**](DiskCache.md) |  | [optional] 
**creation_timestamp** | **int** | Epoch for disk creation time | [optional] 
**deletion_timestamp** | **int** | Epoch time of deletion | [optional] 
**description** | **str** | Disk description | [optional] 
**destroy_queue_timestamp** | **int** | Epoch timestamp of destroy queue | [optional] 
**exposed** | **bool** | True if the disk is exposed via NBD | [optional] 
**exposing_machine_id** | **int** | Machine id used for exposing the disk | [optional] 
**gid** | **int** | Grid ID | [optional] 
**has_serial** | **bool** | Indicates if we should add serial to disk | [optional] 
**host** | **str** | Edge host for the storagedriver | [optional] 
**id** | **int** | Disk identifier | [optional] 
**io_tune** | [**DiskIoTune**](DiskIoTune.md) |  | [optional] 
**last_destroy_attempt_timestamp** | **int** | Epoch timestamp of last attempt at destroying the disk | [optional] 
**meta_data** | **object** | Metadata key value store related to the image | [optional] 
**model_** | **str** | Model to virtualize disk | [optional] 
**name** | **str** | Disk name | [optional] 
**node_id** | **int** | Node id where the disk is on (used for localstorage) | [optional] 
**order** | **int** | Order of the disk (as will be shown in OS) | [optional] 
**path** | **str** | Path to the vdisk in the storage platform | [optional] 
**port** | **int** | Edge port for the storagedriver | [optional] 
**provisioned_size** | **int** | Provisioned size of disk in GiB | [optional] 
**raid** | **str** | If the physical disk created with raid | [optional] 
**reality_device_number** | **int** | Number a device gets after connect | [optional] 
**reference_id** | **str** | Vdisk guid in the storage platform | [optional] 
**snapshot_policy** | [**DiskCache**](DiskCache.md) |  | [optional] 
**status** | **str** | Disk status | [optional] 
**tags** | **list[str]** | A tags list for internal usage | [optional] 
**type** | **str** | Disk type | [optional] 
**update_timestamp** | **int** | Epoch time of update disk | [optional] 
**used_size** | **int** | Used capacity of disk in MB | [optional] 
**versions** | [**list[DiskDiskVersion]**](DiskDiskVersion.md) | List of disk versions. Only applicable to cdroms. | [optional] 
**backup_snapshot_ratio** | **float** | Backup snapshot ratio | [optional] 
**backup_blocksize** | **int** | Backup block size | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


