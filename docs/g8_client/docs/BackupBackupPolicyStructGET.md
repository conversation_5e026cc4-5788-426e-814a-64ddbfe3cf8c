# BackupBackupPolicyStructGET

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **int** | Id | [optional] 
**name** | **str** | Name of the backup target | 
**target** | **int** | Backup target id | [optional] 
**snapshot_policy** | [**BackupBackupPolicyStructCREATESnapshotPolicy**](BackupBackupPolicyStructCREATESnapshotPolicy.md) |  | 
**cron** | **str** | Cron which defines when backups need to be created | 
**restic_retention_flags** | **str** | Restic command line flags | 
**failure_report_email** | **str** | The email to report to when the backup fails | 
**metadata** | **object** | metadata | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


