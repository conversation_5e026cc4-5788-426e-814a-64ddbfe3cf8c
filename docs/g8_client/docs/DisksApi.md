# meneja.lib.clients.g8.lib.DisksApi

All URIs are relative to *https://localhost/api/1*

Method | HTTP request | Description
------------- | ------------- | -------------
[**g8_admin_add_disk**](DisksApi.md#g8_admin_add_disk) | **POST** /disks | Create Disk
[**g8_admin_delete_disk**](DisksApi.md#g8_admin_delete_disk) | **DELETE** /disks/{disk_id} | Delete disk
[**g8_admin_disk_add_cache**](DisksApi.md#g8_admin_disk_add_cache) | **POST** /disks/{disk_id}/cache | Configure cache on disk
[**g8_admin_disk_disk_backup_blocksize**](DisksApi.md#g8_admin_disk_disk_backup_blocksize) | **POST** /disks/{disk_id}/backup_block_size | Set disk backup block size
[**g8_admin_disk_disk_backup_ratio**](DisksApi.md#g8_admin_disk_disk_backup_ratio) | **POST** /disks/{disk_id}/backup_snapshot_ratio | Set disk backup snapshot ratio
[**g8_admin_disk_set_srub**](DisksApi.md#g8_admin_disk_set_srub) | **POST** /disks/{disk_id}/scrub | Configure cache on disk
[**g8_admin_get_disk**](DisksApi.md#g8_admin_get_disk) | **GET** /disks/{disk_id} | Get disks
[**g8_admin_list_disk**](DisksApi.md#g8_admin_list_disk) | **GET** /disks | List disks


# **g8_admin_add_disk**
> Disk g8_admin_add_disk(payload, x_fields=x_fields)

Create Disk

Add Disk in the G8

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.DisksApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
payload = meneja.lib.clients.g8.lib.DisksDiskCreateStruct() # DisksDiskCreateStruct | 
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # Create Disk
    api_response = api_instance.g8_admin_add_disk(payload, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling DisksApi->g8_admin_add_disk: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **payload** | [**DisksDiskCreateStruct**](DisksDiskCreateStruct.md)|  | 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**Disk**](Disk.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g8_admin_delete_disk**
> g8_admin_delete_disk(disk_id)

Delete disk

Delete disk

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.DisksApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
disk_id = 56 # int | 

try:
    # Delete disk
    api_instance.g8_admin_delete_disk(disk_id)
except ApiException as e:
    print("Exception when calling DisksApi->g8_admin_delete_disk: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **disk_id** | **int**|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g8_admin_disk_add_cache**
> g8_admin_disk_add_cache(disk_id, payload)

Configure cache on disk

Configure cache on disk

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.DisksApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
disk_id = 56 # int | 
payload = meneja.lib.clients.g8.lib.DisksDiskCacheStruct() # DisksDiskCacheStruct | 

try:
    # Configure cache on disk
    api_instance.g8_admin_disk_add_cache(disk_id, payload)
except ApiException as e:
    print("Exception when calling DisksApi->g8_admin_disk_add_cache: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **disk_id** | **int**|  | 
 **payload** | [**DisksDiskCacheStruct**](DisksDiskCacheStruct.md)|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g8_admin_disk_disk_backup_blocksize**
> g8_admin_disk_disk_backup_blocksize(disk_id, payload)

Set disk backup block size

Set disk backup block size

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.DisksApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
disk_id = 56 # int | 
payload = meneja.lib.clients.g8.lib.DisksDiskBackupBlocksizeStruct() # DisksDiskBackupBlocksizeStruct | 

try:
    # Set disk backup block size
    api_instance.g8_admin_disk_disk_backup_blocksize(disk_id, payload)
except ApiException as e:
    print("Exception when calling DisksApi->g8_admin_disk_disk_backup_blocksize: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **disk_id** | **int**|  | 
 **payload** | [**DisksDiskBackupBlocksizeStruct**](DisksDiskBackupBlocksizeStruct.md)|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g8_admin_disk_disk_backup_ratio**
> g8_admin_disk_disk_backup_ratio(disk_id, payload)

Set disk backup snapshot ratio

Set physical disk backup snapshot ratio

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.DisksApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
disk_id = 56 # int | 
payload = meneja.lib.clients.g8.lib.DisksDiskBackupRatioStruct() # DisksDiskBackupRatioStruct | 

try:
    # Set disk backup snapshot ratio
    api_instance.g8_admin_disk_disk_backup_ratio(disk_id, payload)
except ApiException as e:
    print("Exception when calling DisksApi->g8_admin_disk_disk_backup_ratio: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **disk_id** | **int**|  | 
 **payload** | [**DisksDiskBackupRatioStruct**](DisksDiskBackupRatioStruct.md)|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g8_admin_disk_set_srub**
> g8_admin_disk_set_srub(disk_id, payload)

Configure cache on disk

Configure scrub on disk

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.DisksApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
disk_id = 56 # int | 
payload = meneja.lib.clients.g8.lib.DisksDiskScrubStruct() # DisksDiskScrubStruct | 

try:
    # Configure cache on disk
    api_instance.g8_admin_disk_set_srub(disk_id, payload)
except ApiException as e:
    print("Exception when calling DisksApi->g8_admin_disk_set_srub: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **disk_id** | **int**|  | 
 **payload** | [**DisksDiskScrubStruct**](DisksDiskScrubStruct.md)|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g8_admin_get_disk**
> Disk g8_admin_get_disk(disk_id, x_fields=x_fields)

Get disks

Get disk

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.DisksApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
disk_id = 56 # int | 
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # Get disks
    api_response = api_instance.g8_admin_get_disk(disk_id, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling DisksApi->g8_admin_get_disk: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **disk_id** | **int**|  | 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**Disk**](Disk.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g8_admin_list_disk**
> DisksPagination g8_admin_list_disk(limit=limit, start_after=start_after, sort_by=sort_by, sort_direction=sort_direction, id=id, name=name, x_fields=x_fields)

List disks

List disks in the G8

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.DisksApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
limit = 25 # int | Flag to limit the amount of results. (optional) (default to 25)
start_after = 56 # int | Start returning records after index (optional)
sort_by = 'sort_by_example' # str | sorting field (optional)
sort_direction = 1 # int | sorting direction. 1 for asc and -1 for desc (optional) (default to 1)
id = 56 # int | Disk id filter (optional)
name = 'name_example' # str | Disk  name filter (optional)
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # List disks
    api_response = api_instance.g8_admin_list_disk(limit=limit, start_after=start_after, sort_by=sort_by, sort_direction=sort_direction, id=id, name=name, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling DisksApi->g8_admin_list_disk: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **limit** | **int**| Flag to limit the amount of results. | [optional] [default to 25]
 **start_after** | **int**| Start returning records after index | [optional] 
 **sort_by** | **str**| sorting field | [optional] 
 **sort_direction** | **int**| sorting direction. 1 for asc and -1 for desc | [optional] [default to 1]
 **id** | **int**| Disk id filter | [optional] 
 **name** | **str**| Disk  name filter | [optional] 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**DisksPagination**](DisksPagination.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

