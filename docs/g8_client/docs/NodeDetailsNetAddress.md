# NodeDetailsNetAddress

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**cidr** | **list[str]** | Classless inter-domain routing for the interface | [optional] 
**index** | **int** | An integer id for the network address | [optional] 
**ip** | **list[str]** | list of ip addresses for the interface | [optional] 
**mac** | **str** | Mac address for the interface | [optional] 
**mtu** | **int** | The maximum transmission unit (MTU) of the network interface | [optional] 
**name** | **str** | Network interface name | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


