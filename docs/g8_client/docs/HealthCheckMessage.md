# HealthCheckMessage

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**category** | **str** | category of the health check message | [optional] 
**last_error_timestamp** | **int** | Epoch for when of the last error occurred | [optional] 
**message** | **str** | The message body for the health check | [optional] 
**node_id** | **int** | Node id (Optional) where the message is related to | [optional] 
**path** | **str** | Optional file system path if the check is related to volumes | [optional] 
**state** | **str** | the state of the check OK, WARNING, ERROR | [optional] 
**uid** | **str** | unique id for the messega | [optional] 
**mute_details** | [**HealthCheckMessageMuteDetails**](HealthCheckMessageMuteDetails.md) |  | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


