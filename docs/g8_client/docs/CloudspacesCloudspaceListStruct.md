# CloudspacesCloudspaceListStruct

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **int** | Cloudspace unique id | 
**name** | **str** | Cloudspace name | 
**account** | [**CloudspacesCloudspaceDetailsStructAccount**](CloudspacesCloudspaceDetailsStructAccount.md) |  | 
**networks** | [**list[CloudspacesNetworkStruct]**](CloudspacesNetworkStruct.md) | Cloudspace networks info | 
**status** | **str** | Cloudspace status | 
**creation_time** | **int** | Epoch for cloudspace creation timestamp | 
**external_networks** | [**list[ExternalNetworkListStruct]**](ExternalNetworkListStruct.md) | Cloudspace extrenal networks | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


