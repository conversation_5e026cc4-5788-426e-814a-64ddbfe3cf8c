# meneja.lib.clients.g8.lib.VirtualMachinesApi

All URIs are relative to *https://localhost/api/1*

Method | HTTP request | Description
------------- | ------------- | -------------
[**assign_backup_policy**](VirtualMachinesApi.md#assign_backup_policy) | **POST** /virtual-machines/{vm_id}/backup_policies | Assign backup policy
[**create_vm_from_backup**](VirtualMachinesApi.md#create_vm_from_backup) | **POST** /virtual-machines/create_from_backup | Create vm from backup
[**g8_vm_attach_vgpu**](VirtualMachinesApi.md#g8_vm_attach_vgpu) | **POST** /virtual-machines/{vm_id}/vgpus | Attach vgpu
[**g8_vm_detach_vgpu**](VirtualMachinesApi.md#g8_vm_detach_vgpu) | **DELETE** /virtual-machines/{vm_id}/vgpus/{vgpu_guid} | Detach vgpu
[**get_g8_admin_vm_by_id**](VirtualMachinesApi.md#get_g8_admin_vm_by_id) | **GET** /virtual-machines/{vm_id} | Get vm by id
[**get_virtual_machine_placement**](VirtualMachinesApi.md#get_virtual_machine_placement) | **GET** /virtual-machines/placement | Get virtual machine placement
[**get_vm_restore_progress**](VirtualMachinesApi.md#get_vm_restore_progress) | **GET** /virtual-machines/{vm_id}/restore_progress | Get vm restore progress
[**list_g8_admin_vmachines**](VirtualMachinesApi.md#list_g8_admin_vmachines) | **GET** /virtual-machines | List Vmachines
[**unassign_backup_policy**](VirtualMachinesApi.md#unassign_backup_policy) | **DELETE** /virtual-machines/{vm_id}/backup_policies/{policy_id} | Unassign backup policy


# **assign_backup_policy**
> assign_backup_policy(vm_id, payload)

Assign backup policy

Assign backup policy

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.VirtualMachinesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
vm_id = 56 # int | 
payload = meneja.lib.clients.g8.lib.VmachinesVMBackupPolicyStruct() # VmachinesVMBackupPolicyStruct | 

try:
    # Assign backup policy
    api_instance.assign_backup_policy(vm_id, payload)
except ApiException as e:
    print("Exception when calling VirtualMachinesApi->assign_backup_policy: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **vm_id** | **int**|  | 
 **payload** | [**VmachinesVMBackupPolicyStruct**](VmachinesVMBackupPolicyStruct.md)|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **create_vm_from_backup**
> VmachinesCreateVMFromBackupResponseStruct create_vm_from_backup(payload, x_fields=x_fields)

Create vm from backup

Create VM from backup

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.VirtualMachinesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
payload = meneja.lib.clients.g8.lib.VmachinesCreateVMFromBackupStructCREATE() # VmachinesCreateVMFromBackupStructCREATE | 
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # Create vm from backup
    api_response = api_instance.create_vm_from_backup(payload, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling VirtualMachinesApi->create_vm_from_backup: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **payload** | [**VmachinesCreateVMFromBackupStructCREATE**](VmachinesCreateVMFromBackupStructCREATE.md)|  | 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**VmachinesCreateVMFromBackupResponseStruct**](VmachinesCreateVMFromBackupResponseStruct.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g8_vm_attach_vgpu**
> g8_vm_attach_vgpu(vm_id, payload)

Attach vgpu

Attach VGPU to the vm

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.VirtualMachinesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
vm_id = 56 # int | 
payload = meneja.lib.clients.g8.lib.VmachinesVmDeviceAttachStruct() # VmachinesVmDeviceAttachStruct | 

try:
    # Attach vgpu
    api_instance.g8_vm_attach_vgpu(vm_id, payload)
except ApiException as e:
    print("Exception when calling VirtualMachinesApi->g8_vm_attach_vgpu: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **vm_id** | **int**|  | 
 **payload** | [**VmachinesVmDeviceAttachStruct**](VmachinesVmDeviceAttachStruct.md)|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g8_vm_detach_vgpu**
> g8_vm_detach_vgpu(vm_id, vgpu_guid)

Detach vgpu

Detach VGPU from the vm

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.VirtualMachinesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
vm_id = 56 # int | 
vgpu_guid = 'vgpu_guid_example' # str | 

try:
    # Detach vgpu
    api_instance.g8_vm_detach_vgpu(vm_id, vgpu_guid)
except ApiException as e:
    print("Exception when calling VirtualMachinesApi->g8_vm_detach_vgpu: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **vm_id** | **int**|  | 
 **vgpu_guid** | **str**|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_g8_admin_vm_by_id**
> VmachinesVmDetailsStruct get_g8_admin_vm_by_id(vm_id, x_fields=x_fields)

Get vm by id

Get details of a specific Vm

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.VirtualMachinesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
vm_id = 56 # int | 
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # Get vm by id
    api_response = api_instance.get_g8_admin_vm_by_id(vm_id, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling VirtualMachinesApi->get_g8_admin_vm_by_id: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **vm_id** | **int**|  | 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**VmachinesVmDetailsStruct**](VmachinesVmDetailsStruct.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_virtual_machine_placement**
> VirtualMachinePlacementStruct get_virtual_machine_placement(x_fields=x_fields)

Get virtual machine placement

Produces a complete machine placement overview

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.VirtualMachinesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # Get virtual machine placement
    api_response = api_instance.get_virtual_machine_placement(x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling VirtualMachinesApi->get_virtual_machine_placement: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**VirtualMachinePlacementStruct**](VirtualMachinePlacementStruct.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_vm_restore_progress**
> VmachinesGetVMRestoreProgressStruct get_vm_restore_progress(vm_id, x_fields=x_fields)

Get vm restore progress

Get VM restore progress info

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.VirtualMachinesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
vm_id = 56 # int | 
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # Get vm restore progress
    api_response = api_instance.get_vm_restore_progress(vm_id, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling VirtualMachinesApi->get_vm_restore_progress: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **vm_id** | **int**|  | 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**VmachinesGetVMRestoreProgressStruct**](VmachinesGetVMRestoreProgressStruct.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **list_g8_admin_vmachines**
> VmachinesPagination list_g8_admin_vmachines(limit=limit, start_after=start_after, sort_by=sort_by, sort_direction=sort_direction, id=id, name=name, status=status, cloudspace_id=cloudspace_id, cloudspace_name=cloudspace_name, account_id=account_id, account_name=account_name, node_id=node_id, node_name=node_name, x_fields=x_fields)

List Vmachines

Lists Vmachines in the G8

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.VirtualMachinesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
limit = 25 # int | Flag to limit the amount of results. (optional) (default to 25)
start_after = 56 # int | Start returning records after index (optional)
sort_by = 'sort_by_example' # str | sorting field (optional)
sort_direction = 1 # int | sorting direction. 1 for asc and -1 for desc (optional) (default to 1)
id = 56 # int | search id (optional)
name = 'name_example' # str | search name (optional)
status = 'status_example' # str | search status (optional)
cloudspace_id = 56 # int | search with cloudspace id (optional)
cloudspace_name = 'cloudspace_name_example' # str | search with cloudspace name (optional)
account_id = 56 # int | search with account id (optional)
account_name = 'account_name_example' # str | search with account name (optional)
node_id = 56 # int | search with node id (optional)
node_name = 'node_name_example' # str | search with node name (optional)
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # List Vmachines
    api_response = api_instance.list_g8_admin_vmachines(limit=limit, start_after=start_after, sort_by=sort_by, sort_direction=sort_direction, id=id, name=name, status=status, cloudspace_id=cloudspace_id, cloudspace_name=cloudspace_name, account_id=account_id, account_name=account_name, node_id=node_id, node_name=node_name, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling VirtualMachinesApi->list_g8_admin_vmachines: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **limit** | **int**| Flag to limit the amount of results. | [optional] [default to 25]
 **start_after** | **int**| Start returning records after index | [optional] 
 **sort_by** | **str**| sorting field | [optional] 
 **sort_direction** | **int**| sorting direction. 1 for asc and -1 for desc | [optional] [default to 1]
 **id** | **int**| search id | [optional] 
 **name** | **str**| search name | [optional] 
 **status** | **str**| search status | [optional] 
 **cloudspace_id** | **int**| search with cloudspace id | [optional] 
 **cloudspace_name** | **str**| search with cloudspace name | [optional] 
 **account_id** | **int**| search with account id | [optional] 
 **account_name** | **str**| search with account name | [optional] 
 **node_id** | **int**| search with node id | [optional] 
 **node_name** | **str**| search with node name | [optional] 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**VmachinesPagination**](VmachinesPagination.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **unassign_backup_policy**
> unassign_backup_policy(vm_id, policy_id)

Unassign backup policy

Unassign backup policy

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.VirtualMachinesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
vm_id = 56 # int | 
policy_id = 56 # int | 

try:
    # Unassign backup policy
    api_instance.unassign_backup_policy(vm_id, policy_id)
except ApiException as e:
    print("Exception when calling VirtualMachinesApi->unassign_backup_policy: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **vm_id** | **int**|  | 
 **policy_id** | **int**|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

