# meneja.lib.clients.g8.lib.ExternalnetworksApi

All URIs are relative to *https://localhost/api/1*

Method | HTTP request | Description
------------- | ------------- | -------------
[**add_g8_admin_external_network**](ExternalnetworksApi.md#add_g8_admin_external_network) | **POST** /externalnetworks | Create ExternalNetwork
[**add_g8_admin_external_network_accounts**](ExternalnetworksApi.md#add_g8_admin_external_network_accounts) | **POST** /externalnetworks/{externalnetwork_id}/accounts | Add accounts to externalnetwork
[**add_g8_admin_external_network_ips**](ExternalnetworksApi.md#add_g8_admin_external_network_ips) | **POST** /externalnetworks/{externalnetwork_id}/ips | Add external network ips
[**delete_g8_admin_external_network_by_id**](ExternalnetworksApi.md#delete_g8_admin_external_network_by_id) | **DELETE** /externalnetworks/{externalnetwork_id} | Delete external network by id
[**get_g8_admin_external_network_by_id**](ExternalnetworksApi.md#get_g8_admin_external_network_by_id) | **GET** /externalnetworks/{externalnetwork_id} | Get external network by id
[**get_g8_admin_external_network_ips**](ExternalnetworksApi.md#get_g8_admin_external_network_ips) | **GET** /externalnetworks/{externalnetwork_id}/ips | Get external network ips
[**list_g8_admin_external_networks**](ExternalnetworksApi.md#list_g8_admin_external_networks) | **GET** /externalnetworks | List ExternalNetworks
[**remove_g8_admin_external_network_accounts**](ExternalnetworksApi.md#remove_g8_admin_external_network_accounts) | **DELETE** /externalnetworks/{externalnetwork_id}/accounts/{account_id} | Add accounts to externalnetwork
[**remove_g8_admin_external_network_ip**](ExternalnetworksApi.md#remove_g8_admin_external_network_ip) | **DELETE** /externalnetworks/{externalnetwork_id}/ips/{ipaddress} | Remove specific IP from ExternalNetwork
[**update_g8_admin_external_network**](ExternalnetworksApi.md#update_g8_admin_external_network) | **PATCH** /externalnetworks/{externalnetwork_id} | Update ExternalNetwork


# **add_g8_admin_external_network**
> ExternalNetworksExternalNetworkStruct add_g8_admin_external_network(payload, x_fields=x_fields)

Create ExternalNetwork

Add ExternalNetwork in the G8

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.ExternalnetworksApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
payload = meneja.lib.clients.g8.lib.ExternalNetworksExternalNetworkCreateStruct() # ExternalNetworksExternalNetworkCreateStruct | 
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # Create ExternalNetwork
    api_response = api_instance.add_g8_admin_external_network(payload, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling ExternalnetworksApi->add_g8_admin_external_network: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **payload** | [**ExternalNetworksExternalNetworkCreateStruct**](ExternalNetworksExternalNetworkCreateStruct.md)|  | 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**ExternalNetworksExternalNetworkStruct**](ExternalNetworksExternalNetworkStruct.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **add_g8_admin_external_network_accounts**
> add_g8_admin_external_network_accounts(externalnetwork_id, payload)

Add accounts to externalnetwork

Add accounts to ExternalNetwork

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.ExternalnetworksApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
externalnetwork_id = 56 # int | 
payload = meneja.lib.clients.g8.lib.AccountIds() # AccountIds | 

try:
    # Add accounts to externalnetwork
    api_instance.add_g8_admin_external_network_accounts(externalnetwork_id, payload)
except ApiException as e:
    print("Exception when calling ExternalnetworksApi->add_g8_admin_external_network_accounts: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **externalnetwork_id** | **int**|  | 
 **payload** | [**AccountIds**](AccountIds.md)|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **add_g8_admin_external_network_ips**
> add_g8_admin_external_network_ips(externalnetwork_id, payload)

Add external network ips

Add specific IPs to ExternalNetwork

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.ExternalnetworksApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
externalnetwork_id = 56 # int | 
payload = meneja.lib.clients.g8.lib.IpShorts() # IpShorts | 

try:
    # Add external network ips
    api_instance.add_g8_admin_external_network_ips(externalnetwork_id, payload)
except ApiException as e:
    print("Exception when calling ExternalnetworksApi->add_g8_admin_external_network_ips: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **externalnetwork_id** | **int**|  | 
 **payload** | [**IpShorts**](IpShorts.md)|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete_g8_admin_external_network_by_id**
> delete_g8_admin_external_network_by_id(externalnetwork_id)

Delete external network by id

Delete a specific ExternalNetwork

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.ExternalnetworksApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
externalnetwork_id = 56 # int | 

try:
    # Delete external network by id
    api_instance.delete_g8_admin_external_network_by_id(externalnetwork_id)
except ApiException as e:
    print("Exception when calling ExternalnetworksApi->delete_g8_admin_external_network_by_id: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **externalnetwork_id** | **int**|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_g8_admin_external_network_by_id**
> ExternalNetworksExternalNetworkStruct get_g8_admin_external_network_by_id(externalnetwork_id, x_fields=x_fields)

Get external network by id

Get details of a specific ExternalNetwork

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.ExternalnetworksApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
externalnetwork_id = 56 # int | 
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # Get external network by id
    api_response = api_instance.get_g8_admin_external_network_by_id(externalnetwork_id, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling ExternalnetworksApi->get_g8_admin_external_network_by_id: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **externalnetwork_id** | **int**|  | 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**ExternalNetworksExternalNetworkStruct**](ExternalNetworksExternalNetworkStruct.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_g8_admin_external_network_ips**
> ExternalNetworkIpStructs get_g8_admin_external_network_ips(externalnetwork_id, x_fields=x_fields)

Get external network ips

Get details of a specific ExternalNetwork

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.ExternalnetworksApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
externalnetwork_id = 56 # int | 
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # Get external network ips
    api_response = api_instance.get_g8_admin_external_network_ips(externalnetwork_id, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling ExternalnetworksApi->get_g8_admin_external_network_ips: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **externalnetwork_id** | **int**|  | 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**ExternalNetworkIpStructs**](ExternalNetworkIpStructs.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **list_g8_admin_external_networks**
> ExternalNetworksPagination list_g8_admin_external_networks(limit=limit, start_after=start_after, sort_by=sort_by, sort_direction=sort_direction, account_ids=account_ids, name=name, x_fields=x_fields)

List ExternalNetworks

Lists ExternalNetworks in the G8

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.ExternalnetworksApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
limit = 25 # int | Flag to limit the amount of results. (optional) (default to 25)
start_after = 56 # int | Start returning records after index (optional)
sort_by = 'sort_by_example' # str | sorting field (optional)
sort_direction = 1 # int | sorting direction. 1 for asc and -1 for desc (optional) (default to 1)
account_ids = [56] # list[int] | search for externalnetworks accessible by account_ids (to include public add 0)separated by a comma (optional)
name = 'name_example' # str | search on name (optional)
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # List ExternalNetworks
    api_response = api_instance.list_g8_admin_external_networks(limit=limit, start_after=start_after, sort_by=sort_by, sort_direction=sort_direction, account_ids=account_ids, name=name, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling ExternalnetworksApi->list_g8_admin_external_networks: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **limit** | **int**| Flag to limit the amount of results. | [optional] [default to 25]
 **start_after** | **int**| Start returning records after index | [optional] 
 **sort_by** | **str**| sorting field | [optional] 
 **sort_direction** | **int**| sorting direction. 1 for asc and -1 for desc | [optional] [default to 1]
 **account_ids** | [**list[int]**](int.md)| search for externalnetworks accessible by account_ids (to include public add 0)separated by a comma | [optional] 
 **name** | **str**| search on name | [optional] 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**ExternalNetworksPagination**](ExternalNetworksPagination.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **remove_g8_admin_external_network_accounts**
> remove_g8_admin_external_network_accounts(externalnetwork_id, account_id)

Add accounts to externalnetwork

Remove accounts from ExternalNetwork

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.ExternalnetworksApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
externalnetwork_id = 56 # int | 
account_id = 56 # int | 

try:
    # Add accounts to externalnetwork
    api_instance.remove_g8_admin_external_network_accounts(externalnetwork_id, account_id)
except ApiException as e:
    print("Exception when calling ExternalnetworksApi->remove_g8_admin_external_network_accounts: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **externalnetwork_id** | **int**|  | 
 **account_id** | **int**|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **remove_g8_admin_external_network_ip**
> remove_g8_admin_external_network_ip(externalnetwork_id, ipaddress)

Remove specific IP from ExternalNetwork

Remove specific IP from ExternalNetwork

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.ExternalnetworksApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
externalnetwork_id = 56 # int | 
ipaddress = 'ipaddress_example' # str | 

try:
    # Remove specific IP from ExternalNetwork
    api_instance.remove_g8_admin_external_network_ip(externalnetwork_id, ipaddress)
except ApiException as e:
    print("Exception when calling ExternalnetworksApi->remove_g8_admin_external_network_ip: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **externalnetwork_id** | **int**|  | 
 **ipaddress** | **str**|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **update_g8_admin_external_network**
> ExternalNetworksExternalNetworkStruct update_g8_admin_external_network(externalnetwork_id, payload, x_fields=x_fields)

Update ExternalNetwork

Update ExternalNetwork in the G8

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.ExternalnetworksApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
externalnetwork_id = 56 # int | 
payload = meneja.lib.clients.g8.lib.ExternalNetworksExternalNetworkUpdateStruct() # ExternalNetworksExternalNetworkUpdateStruct | 
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # Update ExternalNetwork
    api_response = api_instance.update_g8_admin_external_network(externalnetwork_id, payload, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling ExternalnetworksApi->update_g8_admin_external_network: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **externalnetwork_id** | **int**|  | 
 **payload** | [**ExternalNetworksExternalNetworkUpdateStruct**](ExternalNetworksExternalNetworkUpdateStruct.md)|  | 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**ExternalNetworksExternalNetworkStruct**](ExternalNetworksExternalNetworkStruct.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

