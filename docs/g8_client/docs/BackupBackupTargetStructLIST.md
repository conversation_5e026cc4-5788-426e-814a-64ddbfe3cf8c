# BackupBackupTargetStructLIST

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **int** | Id of the backup target | [optional] 
**name** | **str** | Name of the backup target | 
**account_ids** | **list[int]** | List of account ids that can use the backup target. If empty, all accounts can use it | [optional] 
**cloudspace_id** | **int** | Cloudspace network to use to access target | 
**cloudspace_name** | **str** | Cloudspace network name | [optional] 
**metadata** | **object** | metadata | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


