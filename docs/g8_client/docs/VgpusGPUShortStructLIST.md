# VgpusGPUShortStructLIST

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**model** | **str** | GPU model | 
**vendor** | **str** | GPU vendor | 
**available_instances** | **int** | Available instances | 
**description** | **str** | Description | [optional] [default to '']
**id** | **str** | GPU id for enabled gpus only | [optional] 
**subdevices_count** | **int** | Number of subdevices of the gpu | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


