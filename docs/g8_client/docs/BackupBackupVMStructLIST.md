# BackupBackupVMStructLIST

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **int** | default | 
**reference_id** | **str** | default | 
**name** | **str** | default | [optional] 
**description** | **str** | default | [optional] 
**hostname** | **str** | default | [optional] 
**memory** | **int** | default | 
**vcpus** | **int** | default | 
**boot_disk_id** | **int** | default | 
**disks** | [**list[BackupBackupDiskStructLIST]**](BackupBackupDiskStructLIST.md) | default | 
**network_interfaces** | [**list[BackupBackupNetworkInterfaceStructLIST]**](BackupBackupNetworkInterfaceStructLIST.md) | default | 
**image** | [**BackupBackupNetworkInterfaceStructGETPciAddress**](BackupBackupNetworkInterfaceStructGETPciAddress.md) |  | 
**metadata** | **object** | default | [optional] 
**userdata** | **object** | default | [optional] 
**vtpm_restic_snapshot_id** | **str** | Restic snapshot id of the vtpm state file | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


