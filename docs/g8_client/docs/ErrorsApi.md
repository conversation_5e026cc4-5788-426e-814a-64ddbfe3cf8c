# meneja.lib.clients.g8.lib.ErrorsApi

All URIs are relative to *https://localhost/api/1*

Method | HTTP request | Description
------------- | ------------- | -------------
[**get_g8_error_condition**](ErrorsApi.md#get_g8_error_condition) | **GET** /errors/{traceback_hash} | Get error condition for the traceback
[**list_g8_error_conditions**](ErrorsApi.md#list_g8_error_conditions) | **GET** /errors/ | List error conditions


# **get_g8_error_condition**
> ErrorLog get_g8_error_condition(traceback_hash, x_fields=x_fields)

Get error condition for the traceback

get error condition

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.ErrorsApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
traceback_hash = 'traceback_hash_example' # str | 
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # Get error condition for the traceback
    api_response = api_instance.get_g8_error_condition(traceback_hash, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling ErrorsApi->get_g8_error_condition: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **traceback_hash** | **str**|  | 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**ErrorLog**](ErrorLog.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **list_g8_error_conditions**
> ErrorLogsPagination list_g8_error_conditions(limit=limit, start_after=start_after, sort_by=sort_by, sort_direction=sort_direction, search=search, x_fields=x_fields)

List error conditions

List error conditions

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.ErrorsApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
limit = 25 # int | Flag to limit the amount of results. (optional) (default to 25)
start_after = 56 # int | Start returning records after index (optional)
sort_by = 'sort_by_example' # str | sorting field (optional)
sort_direction = 1 # int | sorting direction. 1 for asc and -1 for desc (optional) (default to 1)
search = 'search_example' # str | Search for the error (optional)
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # List error conditions
    api_response = api_instance.list_g8_error_conditions(limit=limit, start_after=start_after, sort_by=sort_by, sort_direction=sort_direction, search=search, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling ErrorsApi->list_g8_error_conditions: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **limit** | **int**| Flag to limit the amount of results. | [optional] [default to 25]
 **start_after** | **int**| Start returning records after index | [optional] 
 **sort_by** | **str**| sorting field | [optional] 
 **sort_direction** | **int**| sorting direction. 1 for asc and -1 for desc | [optional] [default to 1]
 **search** | **str**| Search for the error | [optional] 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**ErrorLogsPagination**](ErrorLogsPagination.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

