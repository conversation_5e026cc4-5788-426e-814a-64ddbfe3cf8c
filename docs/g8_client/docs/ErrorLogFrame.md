# ErrorLogFrame

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**filename** | **str** | The filename for the frame | [optional] 
**lineno** | **int** | The line within filename for the frame that was active when the frame was captured | [optional] 
**localz** | **object** | Either None if locals were not supplied, or a dict mapping the name to the repr() of the variable | [optional] 
**name** | **str** | The name of the function or method that was executing when the frame was captured | [optional] 
**source_code** | **str** | Piece of source. -5 lines before lineno till +5 lines after lineno | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


