# meneja.lib.clients.g8.lib.CdromImagesApi

All URIs are relative to *https://localhost/api/1*

Method | HTTP request | Description
------------- | ------------- | -------------
[**get_g8_cdrom_images**](CdromImagesApi.md#get_g8_cdrom_images) | **GET** /cdrom-images/{cdrom_image_id} | Get CDROM image by id
[**list_g8_cdrom_images**](CdromImagesApi.md#list_g8_cdrom_images) | **GET** /cdrom-images | List CDROM images


# **get_g8_cdrom_images**
> CdromCDROMDetailsStruct get_g8_cdrom_images(cdrom_image_id, x_fields=x_fields)

Get CDROM image by id

Get CDROM image by id

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.CdromImagesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
cdrom_image_id = 56 # int | 
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # Get CDROM image by id
    api_response = api_instance.get_g8_cdrom_images(cdrom_image_id, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling CdromImagesApi->get_g8_cdrom_images: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **cdrom_image_id** | **int**|  | 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**CdromCDROMDetailsStruct**](CdromCDROMDetailsStruct.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **list_g8_cdrom_images**
> CDROMPaginationModel list_g8_cdrom_images(limit=limit, start_after=start_after, sort_by=sort_by, sort_direction=sort_direction, id=id, name=name, status=status, access=access, x_fields=x_fields)

List CDROM images

List CDROM images

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.CdromImagesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
limit = 25 # int | Flag to limit the amount of results. (optional) (default to 25)
start_after = 56 # int | Start returning records after index (optional)
sort_by = 'sort_by_example' # str | sorting field (optional)
sort_direction = 1 # int | sorting direction. 1 for asc and -1 for desc (optional) (default to 1)
id = 56 # int | Cdrom id filter (optional)
name = 'name_example' # str | Cdrom name filter (optional)
status = 'status_example' # str | Cdrom status filter (optional)
access = 'access_example' # str | Cdrom access filter (optional)
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # List CDROM images
    api_response = api_instance.list_g8_cdrom_images(limit=limit, start_after=start_after, sort_by=sort_by, sort_direction=sort_direction, id=id, name=name, status=status, access=access, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling CdromImagesApi->list_g8_cdrom_images: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **limit** | **int**| Flag to limit the amount of results. | [optional] [default to 25]
 **start_after** | **int**| Start returning records after index | [optional] 
 **sort_by** | **str**| sorting field | [optional] 
 **sort_direction** | **int**| sorting direction. 1 for asc and -1 for desc | [optional] [default to 1]
 **id** | **int**| Cdrom id filter | [optional] 
 **name** | **str**| Cdrom name filter | [optional] 
 **status** | **str**| Cdrom status filter | [optional] 
 **access** | **str**| Cdrom access filter | [optional] 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**CDROMPaginationModel**](CDROMPaginationModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

