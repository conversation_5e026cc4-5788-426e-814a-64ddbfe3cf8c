# WorkflowsWorkflowStep

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **str** | Globally unique Workflow id | 
**title** | **str** | Short description of the workflow | 
**status** | **str** | Current status of the workflow | 
**created_on** | **float** | Epoch timestamp when the workflow was submitted | 
**queued_on** | **float** | Epoch timestamp when the workflow was queued | 
**picked_up_on** | **float** | Epoch timestamp when the workflow was picked up by a worker | 
**executed_on** | **float** | Epoch timestamp when the workflow was executed | 
**attempts** | **int** | Current attempt | 
**logs** | **object** | Logs emitted in the course of executing the task | 
**worker_name** | **str** | Name of worked which executed the task | 
**on_success** | **object** | TaskStatus of task that is executed on the successful completion of this TaskStatus | [optional] 
**on_failure** | **object** | TaskStatus of task that is executed on the failed completion of this TaskStatus | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


