# Workflow

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **str** | Globally unique workflow id | [optional] 
**title** | **str** | Short description of the workflow | [optional] 
**status** | **str** | Current status of the workflow | [optional] 
**created_on** | **float** | Epoch timestamp when the workflow was submitted | [optional] 
**executed_on** | **float** | Epoch timestamp when the workflow finished executing | [optional] 
**picked_up_on** | **float** | Epoch timestamp when the workflow was picked up by a worker | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


