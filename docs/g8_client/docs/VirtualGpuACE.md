# VirtualGpuACE

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**explicit** | **bool** | True when the ACE is granted by the system as the result of an access grant to a child object. | [optional] 
**guid** | **str** | Globally unique ACE identifier | [optional] 
**right** | **str** | Access right definition | [optional] 
**status** | **str** | Status of acccess control list | [optional] 
**type** | **str** | Access control entry type [USER/GROUP] | [optional] 
**user_group_id** | **str** | ? | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


