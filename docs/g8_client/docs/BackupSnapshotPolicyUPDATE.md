# BackupSnapshotPolicyUPDATE

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**cooperative** | **bool** | If set, it will freeze the VM&#39;s file systems during the snapshot operation | 
**cooperative_failure_behaviour** | **str** |  | [optional] 
**cooperative_timeout** | **int** | Number of seconds that freezing the VM&#39;s file system should take maximally | [optional] 
**timeout** | **int** | Maximum number of seconds to wait on the snapshot creation | 
**retry_pause** | **int** | Number of seconds to pause between retries if cooperative_failure_behaviour is set to RETRY_IF_SNAPSHOT_TIMEOUT | [optional] 
**retry_times** | **int** | Number of times to retry the cooperative snapshot if cooperative_failure_behaviour is set to RETRY_IF_SNAPSHOT_TIMEOUT | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


