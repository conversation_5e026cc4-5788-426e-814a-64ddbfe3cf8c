# BackupBackupStructLIST

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **str** | resource id | [optional] 
**policy** | **int** | Backup policy id | [optional] 
**target** | **int** | Backup target id | [optional] 
**vm_id** | **int** | vm id | [optional] 
**vm_name** | **str** | vm name | [optional] 
**policy_name** | **str** | policy name | [optional] 
**location** | **str** | backup location | [optional] 
**start_time** | **int** | When the backup operation is started | [optional] 
**end_time** | **int** | When the backup operation finishes | [optional] 
**snapshot_time** | **int** | Time when the snapshot was specifically taken on the block devices | [optional] 
**snapshot_end_time** | **int** | Time when the snapshot was successfully taken | [optional] 
**origin** | **str** | Defines who started the backup procedure | [optional] 
**status** | **str** | Backup status | [optional] 
**snapshot_status** | **str** | Backup snapshot status | [optional] 
**failure_reason** | **str** | Defines who started the backup procedure | [optional] 
**failure_description** | **str** | More detailed information to understand why a backup operation failed | [optional] 
**previous_backup** | **str** | Id of the previous backup | [optional] 
**data_size** | **int** | Total backup size in bytes in the backup repository | [optional] 
**delta_size** | **int** | Delta size in bytes that was added to the backup repository compared to the previous backup | [optional] 
**workflow** | **str** | Root task id of the dynaqueue workflow that creates the backup | [optional] 
**account_id** | **int** | account id | [optional] 
**account_name** | **str** | account name | [optional] 
**cloudspace_id** | **int** | cloudspace id | [optional] 
**cloudspace_name** | **str** | cloudspace name | [optional] 
**metadata** | **object** | metadata | [optional] 
**warnings** | **list[str]** | list of warning messages | [optional] 
**creation_timestamp** | **int** | Epoch for backup creation timestamp | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


