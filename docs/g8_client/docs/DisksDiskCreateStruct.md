# DisksDiskCreateStruct

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**account_id** | **int** | Account id | 
**name** | **str** | Disk name | 
**description** | **str** | Disk description | 
**type** | **str** | Type of the disk | 
**raid** | **str** | Create disk with raid | [optional] 
**metadata** | **object** | Disk metadata | [optional] 
**node_id** | **int** | Node id to create the disk on | [optional] 
**iops** | **int** | Max iops disk can perform | [optional] 
**size** | **int** | Disk size in GBytes | [optional] 
**backup_snapshot_ratio** | **float** | default | [optional] 
**backup_blocksize** | **int** | default | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


