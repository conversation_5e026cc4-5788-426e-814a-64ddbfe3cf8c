# ExternalNetworksExternalNetworkUpdateStruct

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**name** | **str** | External network name | [optional] 
**gateway** | **str** | Gateway to the upstream network | [optional] 
**protection** | **bool** | Indicates if this external network has ip-mac protection or not. | [optional] 
**speed_limit** | **int** | Maximum bandwith speed configured on the network interfaces expressed in Kbit. 0 is the default which gets translated into 1 Gbit | [optional] 
**dhcp** | **bool** | Indicates if a dhcp server automatically hands out ip addresses | [optional] 
**ping_ips** | [**list[IpShort]**](IpShort.md) | List of ips to be pinged to check the external network health | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


