# meneja.lib.clients.g8.lib.NodesApi

All URIs are relative to *https://localhost/api/1*

Method | HTTP request | Description
------------- | ------------- | -------------
[**add_g8_admin_allowed_os_categories_to_node**](NodesApi.md#add_g8_admin_allowed_os_categories_to_node) | **POST** /nodes/{node_id}/allowed-os-categories | Add allowed os categories to node
[**add_g8_admin_disallowed_os_categories_to_node**](NodesApi.md#add_g8_admin_disallowed_os_categories_to_node) | **POST** /nodes/{node_id}/disallowed-os-categories | Add accounts to externalnetwork
[**g8_nodes_localstorage_add_disk**](NodesApi.md#g8_nodes_localstorage_add_disk) | **POST** /nodes/localstorage/disks/add | Add disk to localstorage (lvm)
[**get_g8_node_by_id**](NodesApi.md#get_g8_node_by_id) | **GET** /nodes/{node_id} | Get node by id
[**get_g8_node_power_status**](NodesApi.md#get_g8_node_power_status) | **GET** /nodes/status | Return on/of status for nodes and switches
[**list_g8_node_health_checks**](NodesApi.md#list_g8_node_health_checks) | **GET** /nodes/{node_id}/health-checks | List node health checks
[**list_g8_nodes**](NodesApi.md#list_g8_nodes) | **GET** /nodes | List nodes
[**list_g8_nodes_health_summary**](NodesApi.md#list_g8_nodes_health_summary) | **GET** /nodes/health-checks/summary | List health check summary for all nodes
[**mute_g8_node_health_check**](NodesApi.md#mute_g8_node_health_check) | **PATCH** /nodes/{node_id}/health-checks/{health_check_id}/messages/{message_uid}/mute | Mute/un-mute a specific health check
[**remove_g8_admin_allowed_os_categories_to_node**](NodesApi.md#remove_g8_admin_allowed_os_categories_to_node) | **DELETE** /nodes/{node_id}/allowed-os-categories/{category} | Remove allowed os categories from node
[**remove_g8_admin_disallowed_os_categories_to_node**](NodesApi.md#remove_g8_admin_disallowed_os_categories_to_node) | **DELETE** /nodes/{node_id}/disallowed-os-categories/{category} | Remove disallowed os categories from node
[**reset_health_check_failure**](NodesApi.md#reset_health_check_failure) | **POST** /nodes/health-checks/reset-backup-failures | Reset health check failure
[**run_all_g8_node_health_checks**](NodesApi.md#run_all_g8_node_health_checks) | **PUT** /nodes/{node_id}/health-checks | Re-run all health checks for a specific node
[**run_g8_node_health_check**](NodesApi.md#run_g8_node_health_check) | **PUT** /nodes/{node_id}/health-checks/{health_check_id} | Re-run a health checks for a specific node
[**unmute_g8_node_health_check**](NodesApi.md#unmute_g8_node_health_check) | **PATCH** /nodes/{node_id}/health-checks/{health_check_id}/messages/{message_uid}/unmute | Un-mute a specific health check


# **add_g8_admin_allowed_os_categories_to_node**
> add_g8_admin_allowed_os_categories_to_node(node_id, payload)

Add allowed os categories to node

Add allowed-os-categories to node

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.NodesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
node_id = 56 # int | 
payload = meneja.lib.clients.g8.lib.OSCategories() # OSCategories | 

try:
    # Add allowed os categories to node
    api_instance.add_g8_admin_allowed_os_categories_to_node(node_id, payload)
except ApiException as e:
    print("Exception when calling NodesApi->add_g8_admin_allowed_os_categories_to_node: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **node_id** | **int**|  | 
 **payload** | [**OSCategories**](OSCategories.md)|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **add_g8_admin_disallowed_os_categories_to_node**
> add_g8_admin_disallowed_os_categories_to_node(node_id, payload)

Add accounts to externalnetwork

Add disallowed-os-categories to node

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.NodesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
node_id = 56 # int | 
payload = meneja.lib.clients.g8.lib.OSCategories() # OSCategories | 

try:
    # Add accounts to externalnetwork
    api_instance.add_g8_admin_disallowed_os_categories_to_node(node_id, payload)
except ApiException as e:
    print("Exception when calling NodesApi->add_g8_admin_disallowed_os_categories_to_node: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **node_id** | **int**|  | 
 **payload** | [**OSCategories**](OSCategories.md)|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g8_nodes_localstorage_add_disk**
> g8_nodes_localstorage_add_disk(payload)

Add disk to localstorage (lvm)

Add disk to localstorage

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.NodesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
payload = meneja.lib.clients.g8.lib.NodesLocalstorageDiskCreate() # NodesLocalstorageDiskCreate | 

try:
    # Add disk to localstorage (lvm)
    api_instance.g8_nodes_localstorage_add_disk(payload)
except ApiException as e:
    print("Exception when calling NodesApi->g8_nodes_localstorage_add_disk: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **payload** | [**NodesLocalstorageDiskCreate**](NodesLocalstorageDiskCreate.md)|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_g8_node_by_id**
> NodeDetails get_g8_node_by_id(node_id, x_fields=x_fields)

Get node by id

Get details of a specific node

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.NodesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
node_id = 56 # int | 
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # Get node by id
    api_response = api_instance.get_g8_node_by_id(node_id, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling NodesApi->get_g8_node_by_id: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **node_id** | **int**|  | 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**NodeDetails**](NodeDetails.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_g8_node_power_status**
> NodesPowerStatusStruct get_g8_node_power_status(x_fields=x_fields)

Return on/of status for nodes and switches

Get g8 node ON/OFF status

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.NodesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # Return on/of status for nodes and switches
    api_response = api_instance.get_g8_node_power_status(x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling NodesApi->get_g8_node_power_status: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**NodesPowerStatusStruct**](NodesPowerStatusStruct.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **list_g8_node_health_checks**
> HealthCheckPagination list_g8_node_health_checks(node_id, limit=limit, start_after=start_after, sort_by=sort_by, sort_direction=sort_direction, x_fields=x_fields)

List node health checks

list health checks of a specific node

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.NodesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
node_id = 56 # int | 
limit = 25 # int | Flag to limit the amount of results. (optional) (default to 25)
start_after = 56 # int | Start returning records after index (optional)
sort_by = 'sort_by_example' # str | sorting field (optional)
sort_direction = 1 # int | sorting direction. 1 for asc and -1 for desc (optional) (default to 1)
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # List node health checks
    api_response = api_instance.list_g8_node_health_checks(node_id, limit=limit, start_after=start_after, sort_by=sort_by, sort_direction=sort_direction, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling NodesApi->list_g8_node_health_checks: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **node_id** | **int**|  | 
 **limit** | **int**| Flag to limit the amount of results. | [optional] [default to 25]
 **start_after** | **int**| Start returning records after index | [optional] 
 **sort_by** | **str**| sorting field | [optional] 
 **sort_direction** | **int**| sorting direction. 1 for asc and -1 for desc | [optional] [default to 1]
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**HealthCheckPagination**](HealthCheckPagination.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **list_g8_nodes**
> NodesPagination list_g8_nodes(limit=limit, start_after=start_after, sort_by=sort_by, sort_direction=sort_direction, search=search, x_fields=x_fields)

List nodes

Lists nodes running in the G8

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.NodesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
limit = 25 # int | Flag to limit the amount of results. (optional) (default to 25)
start_after = 56 # int | Start returning records after index (optional)
sort_by = 'sort_by_example' # str | sorting field (optional)
sort_direction = 1 # int | sorting direction. 1 for asc and -1 for desc (optional) (default to 1)
search = 'search_example' # str | search id, name, status or roles (optional)
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # List nodes
    api_response = api_instance.list_g8_nodes(limit=limit, start_after=start_after, sort_by=sort_by, sort_direction=sort_direction, search=search, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling NodesApi->list_g8_nodes: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **limit** | **int**| Flag to limit the amount of results. | [optional] [default to 25]
 **start_after** | **int**| Start returning records after index | [optional] 
 **sort_by** | **str**| sorting field | [optional] 
 **sort_direction** | **int**| sorting direction. 1 for asc and -1 for desc | [optional] [default to 1]
 **search** | **str**| search id, name, status or roles | [optional] 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**NodesPagination**](NodesPagination.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **list_g8_nodes_health_summary**
> NodeHealthCheckStatuss list_g8_nodes_health_summary(x_fields=x_fields)

List health check summary for all nodes

List health check summary for all nodes

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.NodesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # List health check summary for all nodes
    api_response = api_instance.list_g8_nodes_health_summary(x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling NodesApi->list_g8_nodes_health_summary: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**NodeHealthCheckStatuss**](NodeHealthCheckStatuss.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **mute_g8_node_health_check**
> mute_g8_node_health_check(node_id, health_check_id, message_uid, payload)

Mute/un-mute a specific health check

Mute/ a health check of a specific node

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.NodesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
node_id = 56 # int | 
health_check_id = 'health_check_id_example' # str | 
message_uid = 'message_uid_example' # str | 
payload = meneja.lib.clients.g8.lib.NodesMuteHealthCheckStruct() # NodesMuteHealthCheckStruct | 

try:
    # Mute/un-mute a specific health check
    api_instance.mute_g8_node_health_check(node_id, health_check_id, message_uid, payload)
except ApiException as e:
    print("Exception when calling NodesApi->mute_g8_node_health_check: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **node_id** | **int**|  | 
 **health_check_id** | **str**|  | 
 **message_uid** | **str**|  | 
 **payload** | [**NodesMuteHealthCheckStruct**](NodesMuteHealthCheckStruct.md)|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **remove_g8_admin_allowed_os_categories_to_node**
> remove_g8_admin_allowed_os_categories_to_node(node_id, category)

Remove allowed os categories from node

Add allowed-os-categories to node

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.NodesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
node_id = 56 # int | 
category = 'category_example' # str | 

try:
    # Remove allowed os categories from node
    api_instance.remove_g8_admin_allowed_os_categories_to_node(node_id, category)
except ApiException as e:
    print("Exception when calling NodesApi->remove_g8_admin_allowed_os_categories_to_node: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **node_id** | **int**|  | 
 **category** | **str**|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **remove_g8_admin_disallowed_os_categories_to_node**
> remove_g8_admin_disallowed_os_categories_to_node(node_id, category)

Remove disallowed os categories from node

Add allowed-os-categories to node

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.NodesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
node_id = 56 # int | 
category = 'category_example' # str | 

try:
    # Remove disallowed os categories from node
    api_instance.remove_g8_admin_disallowed_os_categories_to_node(node_id, category)
except ApiException as e:
    print("Exception when calling NodesApi->remove_g8_admin_disallowed_os_categories_to_node: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **node_id** | **int**|  | 
 **category** | **str**|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **reset_health_check_failure**
> reset_health_check_failure()

Reset health check failure

Reset health check failure

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.NodesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))

try:
    # Reset health check failure
    api_instance.reset_health_check_failure()
except ApiException as e:
    print("Exception when calling NodesApi->reset_health_check_failure: %s\n" % e)
```

### Parameters
This endpoint does not need any parameter.

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **run_all_g8_node_health_checks**
> run_all_g8_node_health_checks(node_id)

Re-run all health checks for a specific node

Re-run health checks of a specific node

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.NodesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
node_id = 56 # int | 

try:
    # Re-run all health checks for a specific node
    api_instance.run_all_g8_node_health_checks(node_id)
except ApiException as e:
    print("Exception when calling NodesApi->run_all_g8_node_health_checks: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **node_id** | **int**|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **run_g8_node_health_check**
> run_g8_node_health_check(node_id, health_check_id)

Re-run a health checks for a specific node

Re-run a health check of a specific node

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.NodesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
node_id = 56 # int | 
health_check_id = 'health_check_id_example' # str | 

try:
    # Re-run a health checks for a specific node
    api_instance.run_g8_node_health_check(node_id, health_check_id)
except ApiException as e:
    print("Exception when calling NodesApi->run_g8_node_health_check: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **node_id** | **int**|  | 
 **health_check_id** | **str**|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **unmute_g8_node_health_check**
> unmute_g8_node_health_check(node_id, health_check_id, message_uid)

Un-mute a specific health check

Un-mute a health check of a specific node

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.NodesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
node_id = 56 # int | 
health_check_id = 'health_check_id_example' # str | 
message_uid = 'message_uid_example' # str | 

try:
    # Un-mute a specific health check
    api_instance.unmute_g8_node_health_check(node_id, health_check_id, message_uid)
except ApiException as e:
    print("Exception when calling NodesApi->unmute_g8_node_health_check: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **node_id** | **int**|  | 
 **health_check_id** | **str**|  | 
 **message_uid** | **str**|  | 

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

