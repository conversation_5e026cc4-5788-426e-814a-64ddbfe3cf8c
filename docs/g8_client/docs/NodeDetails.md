# NodeDetails

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**active** | **bool** | A flag to show wheather the node is active or not | [optional] 
**description** | **str** | Node description | [optional] 
**id** | **int** | sequence id for node | [optional] 
**last_checked_timestamp** | **int** | Epoch of last time the info updated | [optional] 
**name** | **str** | Name oof the node | [optional] 
**net_addresses** | [**list[NodeDetailsNetAddress]**](NodeDetailsNetAddress.md) | Network address details for each network interface in the node | [optional] 
**roles** | **list[str]** | A list of node roles | [optional] 
**status** | **str** | Node status | [optional] 
**total_memory** | **int** | Amount of memory in megabyte for the Node | [optional] 
**virtual_functions** | **int** | Amount of exposed virtual functions | [optional] 
**vm_memory_allocation** | **int** | Virtual machine allocated memory | [optional] 
**vm_memory_capacity** | **int** | Memory that can be allocated by virtual machines | [optional] 
**allowed_os_categories** | **list[str]** | List of allowed_os on categories the node | [optional] 
**disallowed_os_categories** | **list[str]** | List of disallowed_os categories on the node | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


