# meneja.lib.clients.g8.lib.VirtualMachineImagesApi

All URIs are relative to *https://localhost/api/1*

Method | HTTP request | Description
------------- | ------------- | -------------
[**get_g8_virtual_machine_image_by_id**](VirtualMachineImagesApi.md#get_g8_virtual_machine_image_by_id) | **GET** /virtual-machine-images/{image_id} | Get image by id
[**list_g8_virtual_machine_images**](VirtualMachineImagesApi.md#list_g8_virtual_machine_images) | **GET** /virtual-machine-images | List images
[**list_g8_virtual_machines_by_image_id**](VirtualMachineImagesApi.md#list_g8_virtual_machines_by_image_id) | **GET** /virtual-machine-images/{image_id}/virtual-machines | Get vm list by image id


# **get_g8_virtual_machine_image_by_id**
> ImagesImageDetailsStruct get_g8_virtual_machine_image_by_id(image_id, x_fields=x_fields)

Get image by id

Get virtual machine image by id

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.VirtualMachineImagesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
image_id = 56 # int | 
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # Get image by id
    api_response = api_instance.get_g8_virtual_machine_image_by_id(image_id, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling VirtualMachineImagesApi->get_g8_virtual_machine_image_by_id: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **image_id** | **int**|  | 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**ImagesImageDetailsStruct**](ImagesImageDetailsStruct.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **list_g8_virtual_machine_images**
> ImagesPaginationModel list_g8_virtual_machine_images(limit=limit, start_after=start_after, sort_by=sort_by, sort_direction=sort_direction, id=id, name=name, access=access, status=status, x_fields=x_fields)

List images

List virtual machine images

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.VirtualMachineImagesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
limit = 25 # int | Flag to limit the amount of results. (optional) (default to 25)
start_after = 56 # int | Start returning records after index (optional)
sort_by = 'sort_by_example' # str | sorting field (optional)
sort_direction = 1 # int | sorting direction. 1 for asc and -1 for desc (optional) (default to 1)
id = 56 # int | Image id filter (optional)
name = 'name_example' # str | Image name filter (optional)
access = 'access_example' # str | Image access filter (optional)
status = 'status_example' # str | Image status filter (optional)
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # List images
    api_response = api_instance.list_g8_virtual_machine_images(limit=limit, start_after=start_after, sort_by=sort_by, sort_direction=sort_direction, id=id, name=name, access=access, status=status, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling VirtualMachineImagesApi->list_g8_virtual_machine_images: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **limit** | **int**| Flag to limit the amount of results. | [optional] [default to 25]
 **start_after** | **int**| Start returning records after index | [optional] 
 **sort_by** | **str**| sorting field | [optional] 
 **sort_direction** | **int**| sorting direction. 1 for asc and -1 for desc | [optional] [default to 1]
 **id** | **int**| Image id filter | [optional] 
 **name** | **str**| Image name filter | [optional] 
 **access** | **str**| Image access filter | [optional] 
 **status** | **str**| Image status filter | [optional] 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**ImagesPaginationModel**](ImagesPaginationModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **list_g8_virtual_machines_by_image_id**
> VirtualMachinePaginationModel list_g8_virtual_machines_by_image_id(image_id, limit=limit, start_after=start_after, sort_by=sort_by, sort_direction=sort_direction, id=id, name=name, status=status, node=node, x_fields=x_fields)

Get vm list by image id

Get virtual machines list by image id

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.VirtualMachineImagesApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
image_id = 56 # int | 
limit = 25 # int | Flag to limit the amount of results. (optional) (default to 25)
start_after = 56 # int | Start returning records after index (optional)
sort_by = 'sort_by_example' # str | sorting field (optional)
sort_direction = 1 # int | sorting direction. 1 for asc and -1 for desc (optional) (default to 1)
id = 56 # int | Virtual machine id filter (optional)
name = 'name_example' # str | Virtual machine name filter (optional)
status = 'status_example' # str | Virtual machine status filter (optional)
node = 'node_example' # str | virtual machine node (optional)
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # Get vm list by image id
    api_response = api_instance.list_g8_virtual_machines_by_image_id(image_id, limit=limit, start_after=start_after, sort_by=sort_by, sort_direction=sort_direction, id=id, name=name, status=status, node=node, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling VirtualMachineImagesApi->list_g8_virtual_machines_by_image_id: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **image_id** | **int**|  | 
 **limit** | **int**| Flag to limit the amount of results. | [optional] [default to 25]
 **start_after** | **int**| Start returning records after index | [optional] 
 **sort_by** | **str**| sorting field | [optional] 
 **sort_direction** | **int**| sorting direction. 1 for asc and -1 for desc | [optional] [default to 1]
 **id** | **int**| Virtual machine id filter | [optional] 
 **name** | **str**| Virtual machine name filter | [optional] 
 **status** | **str**| Virtual machine status filter | [optional] 
 **node** | **str**| virtual machine node | [optional] 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**VirtualMachinePaginationModel**](VirtualMachinePaginationModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

