# VmachinesVmachineListStruct

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **int** | vm id | 
**name** | **str** | vm name | 
**status** | **str** | vm status | 
**cloudspace** | [**VmachinesVmDetailsStructCloudspace**](VmachinesVmDetailsStructCloudspace.md) |  | 
**node** | [**VmachinesVmDetailsStructNode**](VmachinesVmDetailsStructNode.md) |  | 
**account** | [**VmachinesVmDetailsStructAccount**](VmachinesVmDetailsStructAccount.md) |  | 
**backup_status** | **str** | latest backup status | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


