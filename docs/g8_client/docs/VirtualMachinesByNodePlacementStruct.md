# VirtualMachinesByNodePlacementStruct

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **int** | Node id | 
**name** | **str** | Node name | 
**memory** | **int** | Memory available to virtual machines | 
**sockets** | **int** | Amount of active cpu sockets | 
**cores_per_socket** | **int** | Cores per socket | 
**threads_per_core** | **int** | Threads per cpu core | 
**threads** | **int** | Threads available to virtual machines | 
**cpu_model** | **str** | Model of cpu | 
**categories** | [**list[VirtualMachinesByOsCategoryStruct]**](VirtualMachinesByOsCategoryStruct.md) | Vms running on this node | 
**allowed_os_categories** | **list[str]** | List of allowed_os on categories the node | 
**disallowed_os_categories** | **list[str]** | List of disallowed_os categories on the node | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


