# meneja.lib.clients.g8.lib.DefaultApi

All URIs are relative to *https://localhost/api/1*

Method | HTTP request | Description
------------- | ------------- | -------------
[**get_liveness_check**](DefaultApi.md#get_liveness_check) | **GET** /liveness | API liveness endpoint


# **get_liveness_check**
> get_liveness_check()

API liveness endpoint

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.DefaultApi(meneja.lib.clients.g8.lib.ApiClient(configuration))

try:
    # API liveness endpoint
    api_instance.get_liveness_check()
except ApiException as e:
    print("Exception when calling DefaultApi->get_liveness_check: %s\n" % e)
```

### Parameters
This endpoint does not need any parameter.

### Return type

void (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

