# Gpu

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**description** | **str** | Description | [optional] 
**max_instances** | **int** | Max instances of vgpu can be created | [optional] 
**node_id** | **int** | Node id | [optional] 
**pci_address** | **str** | Pci address | [optional] 
**vendor** | **str** | GPU vendor | [optional] 
**vgpu_multiplier** | **float** | Vgpu multiplier | [optional] 
**vgpu_type_id** | **str** | Vgpu type id | [optional] 
**id** | **str** | GPU id | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


