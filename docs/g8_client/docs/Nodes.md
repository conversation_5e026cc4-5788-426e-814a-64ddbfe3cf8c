# Nodes

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**cpu_count** | **int** | Amount of cpus in the Node | [optional] 
**cpuinfo** | [**NodesCpuinfo**](NodesCpuinfo.md) |  | [optional] 
**id** | **int** | sequence id for node | [optional] 
**name** | **str** | Name oof the node | [optional] 
**roles** | **list[str]** | A list of node roles | [optional] 
**status** | **str** | Node status | [optional] 
**total_memory** | **int** | Amount of memory in megabyte for the Node | [optional] 
**virtual_functions** | **int** | Amount of exposed virtual functions | [optional] 
**vgw_count** | **int** | Virtual firewall count | [optional] 
**vm_count** | **int** | Virtual machine count | [optional] 
**vm_memory_allocation** | **int** | Virtual machine allocated memory | [optional] 
**vm_memory_capacity** | **int** | Memory that can be allocated by virtual machines | [optional] 
**allowed_os_categories** | **list[str]** | List of allowed_os on categories the node | [optional] 
**disallowed_os_categories** | **list[str]** | List of disallowed_os categories on the node | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


