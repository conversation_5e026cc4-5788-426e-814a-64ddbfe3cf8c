# DiskDiskVersion

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**deletion_timestamp** | **int** | Epoch time of deletion | [optional] 
**description** | **str** | Disk version description | [optional] 
**guid** | **str** | guid | [optional] 
**last_destroy_attempt_timestamp** | **int** | Epoch timestamp of last attempt at destroying the disk | [optional] 
**obsolete_timestamp** | **int** | Epoch timestamp when this disk version became obsolete | [optional] 
**path** | **str** | ? | [optional] 
**provisioned_size** | **int** | Provisioned size of disk in GiB | [optional] 
**reference_id** | **str** | ? | [optional] 
**status** | **str** | Disk version status | [optional] 
**tags** | **list[str]** | A tags list for internal usage | [optional] 
**update_time** | **int** | Epoch time of update | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


