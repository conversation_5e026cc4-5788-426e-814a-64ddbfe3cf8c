# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

from dataclasses import dataclass, field
from typing import List, Optional

from meneja.structs.dataclasses import BaseStruct


@dataclass
class IpShort(BaseStruct):
    """Ip address short struct"""

    address: str = field(metadata={"help_text": "Ip address"})


@dataclass
class UsageResourceStruct(BaseStruct):
    """Network usage resource data struct"""

    id: str = field(metadata={"help_text": "id of resource consuming IPAddresses"})
    vco_id: str = field(metadata={"help_text": "vco id of resource"})
    name: str = field(metadata={"help_text": "Name of resource"})
    type: str = field(metadata={"help_text": "Type of resource"})


@dataclass
class AccountInfoStruct(BaseStruct):
    """Short information struct for api response, used with several models and apis"""

    id: int = field(metadata={"help_text": "unique identifier"})
    name: str = field(metadata={"help_text": "Name"})


@dataclass
class ExternalNetworkIpStruct(BaseStruct):
    """External network ip"""

    address: str = field(metadata={"help_text": "Ip address"})
    usage: UsageResourceStruct = field(metadata={"help_text": "Resource data if the ip is used, otherwise None"})


@dataclass
class ExternalNetworkStruct(BaseStruct):
    """Struct for External Network"""

    id: int = field(metadata={"help_text": "External network identifier"})
    name: str = field(metadata={"help_text": "External network name"})
    account_id: Optional[int] = field(
        metadata={
            "help_text": (
                "account id which can use this network. "
                "empty list means it is a public external network. "
                "kept for backwards compitablity and to be removed"
            )
        },
    )
    accounts: Optional[List[AccountInfoStruct]] = field(
        metadata={
            "help_text": "List of accounts which can use the network, " "empty list means the network is public",
        }
    )
    billable: bool = field(metadata={"help_text": "Indicates if billing records need to be gathered for the use"})
    dhcp: bool = field(metadata={"help_text": "Indicates if a dhcp server automatically hands out ip addresses"})
    gateway: str = field(metadata={"help_text": "Gateway to the upstream network"})
    network: str = field(metadata={"help_text": "Network of the pool"})
    subnet_mask: str = field(metadata={"help_text": "Subnet mask of the pool"})

    ips: List[ExternalNetworkIpStruct] = field(
        metadata={"help_text": "List of external ips that can be used for this external network"}
    )
    ping_ips: List[IpShort] = field(
        metadata={"help_text": "List of ips to be pinged to check the external network health"}
    )
    protection: bool = field(metadata={"help_text": "Indicates if this external network has ip-mac protection or not."})
    speed_limit: int = field(
        metadata={
            "help_text": "Maximum bandwith speed configured on the network interfaces "
            "expressed in Kbit. 0 is the default which gets translated into 1 Gbit"
        }
    )
    sriov: bool = field(metadata={"help_text": "Make use of SR-IOV interfaces for this network"})
    vlan: int = field(metadata={"help_text": "Vlan tag used for isolating this external network"})
