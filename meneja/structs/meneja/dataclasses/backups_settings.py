# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, D<PERSON>CLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

from dataclasses import dataclass, field

from meneja.structs.dataclasses import BaseStruct


@dataclass
class BackupSettingsStruct(BaseStruct):
    """Struct to holding backups settings for cloud enablers"""

    allow_backup_override: bool = field(metadata=dict(help_text="Indicates if policies are editable by VCO"))
    require_vm_backup_policies: bool = field(metadata=dict(help_text="Indicates if targets are editable by VCO"))
