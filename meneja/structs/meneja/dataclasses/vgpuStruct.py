# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, <PERSON><PERSON><PERSON><PERSON>UC<PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

from dataclasses import dataclass, field

from meneja.structs.dataclasses import BaseStruct


@dataclass
class VGPUStruct(BaseStruct):
    """Struct used for Listing Customer vGPUs"""

    account_id: int = field(metadata=dict(help_text="Account ID"))
    account_name: str = field(metadata=dict(help_text="Account name"))
    vco: str = field(metadata=dict(help_text="VCO ID"))
    vgpu_name: str = field(metadata=dict(help_text="vGPU name"))
    gpu_id: str = field(metadata=dict(help_text="GPU Profile ID"))
    description: str = field(metadata=dict(help_text="GPU description"))
    status: str = field(metadata=dict(help_text="vGPU status"))
    creation_time: float = field(metadata=dict(help_text="vGPU creation timestamp", default=None))


@dataclass
class GPUStruct(BaseStruct):
    """Struct used for G8 GPUs"""

    node_id: int = field(metadata=dict(help_text="node ID"))
    pci_address: str = field(metadata=dict(help_text="PCI address"))
    vgpu_type_id: str = field(metadata=dict(help_text="GPU Profile ID"))
    description: str = field(metadata=dict(help_text="GPU description"))
    vgpu_multiplier: float = field(metadata=dict(help_text="vGPU multiplier"))
