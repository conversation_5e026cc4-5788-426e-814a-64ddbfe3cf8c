# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, <PERSON><PERSON><PERSON><PERSON>UC<PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@


from dataclasses import dataclass, field
from typing import List

from meneja.structs.dataclasses import BaseStruct


@dataclass
class TopicStruct(BaseStruct):
    """Struct holding topic data in knowledge base repo tree"""

    name: str = field(metadata=dict(help_text="topic file name"))
    path: str = field(metadata=dict(help_text="topic path"))


@dataclass
class KnowledgeBaseCategoryTreeStruct(BaseStruct):
    """Struct holding knowledge base category directory tree structure"""

    name: str = field(metadata=dict(help_text="category name"))
    children: List[TopicStruct] = field(metadata=dict(help_text="list of topics under the category"))
    path: str = field(metadata=dict(help_text="parent category if exists"))
