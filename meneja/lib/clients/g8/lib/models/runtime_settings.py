# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
    G8 API

    RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

    OpenAPI spec version: v4.0
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class RuntimeSettings(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        "allow_backup_override": "bool",
        "require_vm_backup_policies": "bool",
        "backup_failure_reset_timestamp": "int",
    }

    attribute_map = {
        "allow_backup_override": "allow_backup_override",
        "require_vm_backup_policies": "require_vm_backup_policies",
        "backup_failure_reset_timestamp": "backup_failure_reset_timestamp",
    }

    def __init__(
        self,
        allow_backup_override=True,
        require_vm_backup_policies=False,
        backup_failure_reset_timestamp=None,
        _configuration=None,
    ):  # noqa: E501
        """RuntimeSettings - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._allow_backup_override = None
        self._require_vm_backup_policies = None
        self._backup_failure_reset_timestamp = None
        self.discriminator = None

        self.allow_backup_override = allow_backup_override
        self.require_vm_backup_policies = require_vm_backup_policies
        if backup_failure_reset_timestamp is not None:
            self.backup_failure_reset_timestamp = backup_failure_reset_timestamp

    @property
    def allow_backup_override(self):
        """Gets the allow_backup_override of this RuntimeSettings.  # noqa: E501

        Allow or disallow adding backup targets  # noqa: E501

        :return: The allow_backup_override of this RuntimeSettings.  # noqa: E501
        :rtype: bool
        """
        return self._allow_backup_override

    @allow_backup_override.setter
    def allow_backup_override(self, allow_backup_override):
        """Sets the allow_backup_override of this RuntimeSettings.

        Allow or disallow adding backup targets  # noqa: E501

        :param allow_backup_override: The allow_backup_override of this RuntimeSettings.  # noqa: E501
        :type: bool
        """
        if self._configuration.client_side_validation and allow_backup_override is None:
            raise ValueError("Invalid value for `allow_backup_override`, must not be `None`")  # noqa: E501

        self._allow_backup_override = allow_backup_override

    @property
    def require_vm_backup_policies(self):
        """Gets the require_vm_backup_policies of this RuntimeSettings.  # noqa: E501

        When enabled VMs under this group require a backup policy  # noqa: E501

        :return: The require_vm_backup_policies of this RuntimeSettings.  # noqa: E501
        :rtype: bool
        """
        return self._require_vm_backup_policies

    @require_vm_backup_policies.setter
    def require_vm_backup_policies(self, require_vm_backup_policies):
        """Sets the require_vm_backup_policies of this RuntimeSettings.

        When enabled VMs under this group require a backup policy  # noqa: E501

        :param require_vm_backup_policies: The require_vm_backup_policies of this RuntimeSettings.  # noqa: E501
        :type: bool
        """
        if self._configuration.client_side_validation and require_vm_backup_policies is None:
            raise ValueError("Invalid value for `require_vm_backup_policies`, must not be `None`")  # noqa: E501

        self._require_vm_backup_policies = require_vm_backup_policies

    @property
    def backup_failure_reset_timestamp(self):
        """Gets the backup_failure_reset_timestamp of this RuntimeSettings.  # noqa: E501

        Backup failures reset timestamp  # noqa: E501

        :return: The backup_failure_reset_timestamp of this RuntimeSettings.  # noqa: E501
        :rtype: int
        """
        return self._backup_failure_reset_timestamp

    @backup_failure_reset_timestamp.setter
    def backup_failure_reset_timestamp(self, backup_failure_reset_timestamp):
        """Sets the backup_failure_reset_timestamp of this RuntimeSettings.

        Backup failures reset timestamp  # noqa: E501

        :param backup_failure_reset_timestamp: The backup_failure_reset_timestamp of this RuntimeSettings.  # noqa: E501
        :type: int
        """

        self._backup_failure_reset_timestamp = backup_failure_reset_timestamp

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(RuntimeSettings, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RuntimeSettings):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RuntimeSettings):
            return True

        return self.to_dict() != other.to_dict()
