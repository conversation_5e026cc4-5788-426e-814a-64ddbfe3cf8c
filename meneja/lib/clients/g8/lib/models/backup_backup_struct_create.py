# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
    G8 API

    RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

    OpenAPI spec version: v4.0
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class BackupBackupStructCREATE(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {"policy": "int", "vm_id": "int", "metadata": "object"}

    attribute_map = {"policy": "policy", "vm_id": "vm_id", "metadata": "metadata"}

    def __init__(self, policy=None, vm_id=None, metadata=None, _configuration=None):  # noqa: E501
        """BackupBackupStructCREATE - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._policy = None
        self._vm_id = None
        self._metadata = None
        self.discriminator = None

        if policy is not None:
            self.policy = policy
        if vm_id is not None:
            self.vm_id = vm_id
        if metadata is not None:
            self.metadata = metadata

    @property
    def policy(self):
        """Gets the policy of this BackupBackupStructCREATE.  # noqa: E501

        Backup policy id  # noqa: E501

        :return: The policy of this BackupBackupStructCREATE.  # noqa: E501
        :rtype: int
        """
        return self._policy

    @policy.setter
    def policy(self, policy):
        """Sets the policy of this BackupBackupStructCREATE.

        Backup policy id  # noqa: E501

        :param policy: The policy of this BackupBackupStructCREATE.  # noqa: E501
        :type: int
        """

        self._policy = policy

    @property
    def vm_id(self):
        """Gets the vm_id of this BackupBackupStructCREATE.  # noqa: E501

        vm id  # noqa: E501

        :return: The vm_id of this BackupBackupStructCREATE.  # noqa: E501
        :rtype: int
        """
        return self._vm_id

    @vm_id.setter
    def vm_id(self, vm_id):
        """Sets the vm_id of this BackupBackupStructCREATE.

        vm id  # noqa: E501

        :param vm_id: The vm_id of this BackupBackupStructCREATE.  # noqa: E501
        :type: int
        """

        self._vm_id = vm_id

    @property
    def metadata(self):
        """Gets the metadata of this BackupBackupStructCREATE.  # noqa: E501

        metadata  # noqa: E501

        :return: The metadata of this BackupBackupStructCREATE.  # noqa: E501
        :rtype: object
        """
        return self._metadata

    @metadata.setter
    def metadata(self, metadata):
        """Sets the metadata of this BackupBackupStructCREATE.

        metadata  # noqa: E501

        :param metadata: The metadata of this BackupBackupStructCREATE.  # noqa: E501
        :type: object
        """

        self._metadata = metadata

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(BackupBackupStructCREATE, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, BackupBackupStructCREATE):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, BackupBackupStructCREATE):
            return True

        return self.to_dict() != other.to_dict()
