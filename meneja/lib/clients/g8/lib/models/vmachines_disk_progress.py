# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
    G8 API

    RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

    OpenAPI spec version: v4.0
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class VmachinesDiskProgress(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {"id": "int", "name": "str", "type": "str", "size": "int", "status": "str", "progress": "int"}

    attribute_map = {
        "id": "id",
        "name": "name",
        "type": "type",
        "size": "size",
        "status": "status",
        "progress": "progress",
    }

    def __init__(
        self, id=None, name=None, type=None, size=None, status=None, progress=None, _configuration=None
    ):  # noqa: E501
        """VmachinesDiskProgress - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._id = None
        self._name = None
        self._type = None
        self._size = None
        self._status = None
        self._progress = None
        self.discriminator = None

        self.id = id
        self.name = name
        self.type = type
        self.size = size
        self.status = status
        self.progress = progress

    @property
    def id(self):
        """Gets the id of this VmachinesDiskProgress.  # noqa: E501

        default  # noqa: E501

        :return: The id of this VmachinesDiskProgress.  # noqa: E501
        :rtype: int
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this VmachinesDiskProgress.

        default  # noqa: E501

        :param id: The id of this VmachinesDiskProgress.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and id is None:
            raise ValueError("Invalid value for `id`, must not be `None`")  # noqa: E501

        self._id = id

    @property
    def name(self):
        """Gets the name of this VmachinesDiskProgress.  # noqa: E501

        default  # noqa: E501

        :return: The name of this VmachinesDiskProgress.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this VmachinesDiskProgress.

        default  # noqa: E501

        :param name: The name of this VmachinesDiskProgress.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def type(self):
        """Gets the type of this VmachinesDiskProgress.  # noqa: E501

        default  # noqa: E501

        :return: The type of this VmachinesDiskProgress.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this VmachinesDiskProgress.

        default  # noqa: E501

        :param type: The type of this VmachinesDiskProgress.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and type is None:
            raise ValueError("Invalid value for `type`, must not be `None`")  # noqa: E501

        self._type = type

    @property
    def size(self):
        """Gets the size of this VmachinesDiskProgress.  # noqa: E501

        default  # noqa: E501

        :return: The size of this VmachinesDiskProgress.  # noqa: E501
        :rtype: int
        """
        return self._size

    @size.setter
    def size(self, size):
        """Sets the size of this VmachinesDiskProgress.

        default  # noqa: E501

        :param size: The size of this VmachinesDiskProgress.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and size is None:
            raise ValueError("Invalid value for `size`, must not be `None`")  # noqa: E501

        self._size = size

    @property
    def status(self):
        """Gets the status of this VmachinesDiskProgress.  # noqa: E501

        default  # noqa: E501

        :return: The status of this VmachinesDiskProgress.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this VmachinesDiskProgress.

        default  # noqa: E501

        :param status: The status of this VmachinesDiskProgress.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and status is None:
            raise ValueError("Invalid value for `status`, must not be `None`")  # noqa: E501
        allowed_values = ["MODELED", "CREATED", "RESTORING", "COMPLETED"]  # noqa: E501
        if self._configuration.client_side_validation and status not in allowed_values:
            raise ValueError(
                "Invalid value for `status` ({0}), must be one of {1}".format(status, allowed_values)  # noqa: E501
            )

        self._status = status

    @property
    def progress(self):
        """Gets the progress of this VmachinesDiskProgress.  # noqa: E501

        default  # noqa: E501

        :return: The progress of this VmachinesDiskProgress.  # noqa: E501
        :rtype: int
        """
        return self._progress

    @progress.setter
    def progress(self, progress):
        """Sets the progress of this VmachinesDiskProgress.

        default  # noqa: E501

        :param progress: The progress of this VmachinesDiskProgress.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and progress is None:
            raise ValueError("Invalid value for `progress`, must not be `None`")  # noqa: E501

        self._progress = progress

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(VmachinesDiskProgress, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, VmachinesDiskProgress):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, VmachinesDiskProgress):
            return True

        return self.to_dict() != other.to_dict()
