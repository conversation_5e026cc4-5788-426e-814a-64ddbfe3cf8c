# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
    G8 API

    RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

    OpenAPI spec version: v4.0
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class BackupBackupPolicyStructLIST(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        "id": "int",
        "name": "str",
        "target": "int",
        "cron": "str",
        "restic_retention_flags": "str",
        "failure_report_email": "str",
        "metadata": "object",
    }

    attribute_map = {
        "id": "id",
        "name": "name",
        "target": "target",
        "cron": "cron",
        "restic_retention_flags": "restic_retention_flags",
        "failure_report_email": "failure_report_email",
        "metadata": "metadata",
    }

    def __init__(
        self,
        id=None,
        name=None,
        target=None,
        cron=None,
        restic_retention_flags=None,
        failure_report_email=None,
        metadata=None,
        _configuration=None,
    ):  # noqa: E501
        """BackupBackupPolicyStructLIST - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._id = None
        self._name = None
        self._target = None
        self._cron = None
        self._restic_retention_flags = None
        self._failure_report_email = None
        self._metadata = None
        self.discriminator = None

        if id is not None:
            self.id = id
        self.name = name
        if target is not None:
            self.target = target
        self.cron = cron
        self.restic_retention_flags = restic_retention_flags
        self.failure_report_email = failure_report_email
        if metadata is not None:
            self.metadata = metadata

    @property
    def id(self):
        """Gets the id of this BackupBackupPolicyStructLIST.  # noqa: E501

        Id  # noqa: E501

        :return: The id of this BackupBackupPolicyStructLIST.  # noqa: E501
        :rtype: int
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this BackupBackupPolicyStructLIST.

        Id  # noqa: E501

        :param id: The id of this BackupBackupPolicyStructLIST.  # noqa: E501
        :type: int
        """

        self._id = id

    @property
    def name(self):
        """Gets the name of this BackupBackupPolicyStructLIST.  # noqa: E501

        Name of the backup target  # noqa: E501

        :return: The name of this BackupBackupPolicyStructLIST.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this BackupBackupPolicyStructLIST.

        Name of the backup target  # noqa: E501

        :param name: The name of this BackupBackupPolicyStructLIST.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def target(self):
        """Gets the target of this BackupBackupPolicyStructLIST.  # noqa: E501

        Backup target id  # noqa: E501

        :return: The target of this BackupBackupPolicyStructLIST.  # noqa: E501
        :rtype: int
        """
        return self._target

    @target.setter
    def target(self, target):
        """Sets the target of this BackupBackupPolicyStructLIST.

        Backup target id  # noqa: E501

        :param target: The target of this BackupBackupPolicyStructLIST.  # noqa: E501
        :type: int
        """

        self._target = target

    @property
    def cron(self):
        """Gets the cron of this BackupBackupPolicyStructLIST.  # noqa: E501

        Cron which defines when backups need to be created  # noqa: E501

        :return: The cron of this BackupBackupPolicyStructLIST.  # noqa: E501
        :rtype: str
        """
        return self._cron

    @cron.setter
    def cron(self, cron):
        """Sets the cron of this BackupBackupPolicyStructLIST.

        Cron which defines when backups need to be created  # noqa: E501

        :param cron: The cron of this BackupBackupPolicyStructLIST.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and cron is None:
            raise ValueError("Invalid value for `cron`, must not be `None`")  # noqa: E501

        self._cron = cron

    @property
    def restic_retention_flags(self):
        """Gets the restic_retention_flags of this BackupBackupPolicyStructLIST.  # noqa: E501

        Restic command line flags  # noqa: E501

        :return: The restic_retention_flags of this BackupBackupPolicyStructLIST.  # noqa: E501
        :rtype: str
        """
        return self._restic_retention_flags

    @restic_retention_flags.setter
    def restic_retention_flags(self, restic_retention_flags):
        """Sets the restic_retention_flags of this BackupBackupPolicyStructLIST.

        Restic command line flags  # noqa: E501

        :param restic_retention_flags: The restic_retention_flags of this BackupBackupPolicyStructLIST.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and restic_retention_flags is None:
            raise ValueError("Invalid value for `restic_retention_flags`, must not be `None`")  # noqa: E501

        self._restic_retention_flags = restic_retention_flags

    @property
    def failure_report_email(self):
        """Gets the failure_report_email of this BackupBackupPolicyStructLIST.  # noqa: E501

        The email to report to when the backup fails  # noqa: E501

        :return: The failure_report_email of this BackupBackupPolicyStructLIST.  # noqa: E501
        :rtype: str
        """
        return self._failure_report_email

    @failure_report_email.setter
    def failure_report_email(self, failure_report_email):
        """Sets the failure_report_email of this BackupBackupPolicyStructLIST.

        The email to report to when the backup fails  # noqa: E501

        :param failure_report_email: The failure_report_email of this BackupBackupPolicyStructLIST.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and failure_report_email is None:
            raise ValueError("Invalid value for `failure_report_email`, must not be `None`")  # noqa: E501

        self._failure_report_email = failure_report_email

    @property
    def metadata(self):
        """Gets the metadata of this BackupBackupPolicyStructLIST.  # noqa: E501

        metadata  # noqa: E501

        :return: The metadata of this BackupBackupPolicyStructLIST.  # noqa: E501
        :rtype: object
        """
        return self._metadata

    @metadata.setter
    def metadata(self, metadata):
        """Sets the metadata of this BackupBackupPolicyStructLIST.

        metadata  # noqa: E501

        :param metadata: The metadata of this BackupBackupPolicyStructLIST.  # noqa: E501
        :type: object
        """

        self._metadata = metadata

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(BackupBackupPolicyStructLIST, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, BackupBackupPolicyStructLIST):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, BackupBackupPolicyStructLIST):
            return True

        return self.to_dict() != other.to_dict()
