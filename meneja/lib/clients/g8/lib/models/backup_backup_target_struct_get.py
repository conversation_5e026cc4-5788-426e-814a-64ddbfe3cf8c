# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
    G8 API

    RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

    OpenAPI spec version: v4.0
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class BackupBackupTargetStructGET(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        "id": "int",
        "name": "str",
        "account_ids": "list[int]",
        "cloudspace_id": "int",
        "cloudspace_name": "str",
        "s3": "BackupBackupTargetStructCREATES3",
        "metadata": "object",
    }

    attribute_map = {
        "id": "id",
        "name": "name",
        "account_ids": "account_ids",
        "cloudspace_id": "cloudspace_id",
        "cloudspace_name": "cloudspace_name",
        "s3": "s3",
        "metadata": "metadata",
    }

    def __init__(
        self,
        id=None,
        name=None,
        account_ids=None,
        cloudspace_id=None,
        cloudspace_name=None,
        s3=None,
        metadata=None,
        _configuration=None,
    ):  # noqa: E501
        """BackupBackupTargetStructGET - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._id = None
        self._name = None
        self._account_ids = None
        self._cloudspace_id = None
        self._cloudspace_name = None
        self._s3 = None
        self._metadata = None
        self.discriminator = None

        if id is not None:
            self.id = id
        self.name = name
        if account_ids is not None:
            self.account_ids = account_ids
        self.cloudspace_id = cloudspace_id
        if cloudspace_name is not None:
            self.cloudspace_name = cloudspace_name
        self.s3 = s3
        if metadata is not None:
            self.metadata = metadata

    @property
    def id(self):
        """Gets the id of this BackupBackupTargetStructGET.  # noqa: E501

        Id of the backup target  # noqa: E501

        :return: The id of this BackupBackupTargetStructGET.  # noqa: E501
        :rtype: int
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this BackupBackupTargetStructGET.

        Id of the backup target  # noqa: E501

        :param id: The id of this BackupBackupTargetStructGET.  # noqa: E501
        :type: int
        """

        self._id = id

    @property
    def name(self):
        """Gets the name of this BackupBackupTargetStructGET.  # noqa: E501

        Name of the backup target  # noqa: E501

        :return: The name of this BackupBackupTargetStructGET.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this BackupBackupTargetStructGET.

        Name of the backup target  # noqa: E501

        :param name: The name of this BackupBackupTargetStructGET.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def account_ids(self):
        """Gets the account_ids of this BackupBackupTargetStructGET.  # noqa: E501

        List of account ids that can use the backup target. If empty, all accounts can use it  # noqa: E501

        :return: The account_ids of this BackupBackupTargetStructGET.  # noqa: E501
        :rtype: list[int]
        """
        return self._account_ids

    @account_ids.setter
    def account_ids(self, account_ids):
        """Sets the account_ids of this BackupBackupTargetStructGET.

        List of account ids that can use the backup target. If empty, all accounts can use it  # noqa: E501

        :param account_ids: The account_ids of this BackupBackupTargetStructGET.  # noqa: E501
        :type: list[int]
        """

        self._account_ids = account_ids

    @property
    def cloudspace_id(self):
        """Gets the cloudspace_id of this BackupBackupTargetStructGET.  # noqa: E501

        Cloudspace network to use to access target  # noqa: E501

        :return: The cloudspace_id of this BackupBackupTargetStructGET.  # noqa: E501
        :rtype: int
        """
        return self._cloudspace_id

    @cloudspace_id.setter
    def cloudspace_id(self, cloudspace_id):
        """Sets the cloudspace_id of this BackupBackupTargetStructGET.

        Cloudspace network to use to access target  # noqa: E501

        :param cloudspace_id: The cloudspace_id of this BackupBackupTargetStructGET.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and cloudspace_id is None:
            raise ValueError("Invalid value for `cloudspace_id`, must not be `None`")  # noqa: E501

        self._cloudspace_id = cloudspace_id

    @property
    def cloudspace_name(self):
        """Gets the cloudspace_name of this BackupBackupTargetStructGET.  # noqa: E501

        Cloudspace network name  # noqa: E501

        :return: The cloudspace_name of this BackupBackupTargetStructGET.  # noqa: E501
        :rtype: str
        """
        return self._cloudspace_name

    @cloudspace_name.setter
    def cloudspace_name(self, cloudspace_name):
        """Sets the cloudspace_name of this BackupBackupTargetStructGET.

        Cloudspace network name  # noqa: E501

        :param cloudspace_name: The cloudspace_name of this BackupBackupTargetStructGET.  # noqa: E501
        :type: str
        """

        self._cloudspace_name = cloudspace_name

    @property
    def s3(self):
        """Gets the s3 of this BackupBackupTargetStructGET.  # noqa: E501


        :return: The s3 of this BackupBackupTargetStructGET.  # noqa: E501
        :rtype: BackupBackupTargetStructCREATES3
        """
        return self._s3

    @s3.setter
    def s3(self, s3):
        """Sets the s3 of this BackupBackupTargetStructGET.


        :param s3: The s3 of this BackupBackupTargetStructGET.  # noqa: E501
        :type: BackupBackupTargetStructCREATES3
        """
        if self._configuration.client_side_validation and s3 is None:
            raise ValueError("Invalid value for `s3`, must not be `None`")  # noqa: E501

        self._s3 = s3

    @property
    def metadata(self):
        """Gets the metadata of this BackupBackupTargetStructGET.  # noqa: E501

        metadata  # noqa: E501

        :return: The metadata of this BackupBackupTargetStructGET.  # noqa: E501
        :rtype: object
        """
        return self._metadata

    @metadata.setter
    def metadata(self, metadata):
        """Sets the metadata of this BackupBackupTargetStructGET.

        metadata  # noqa: E501

        :param metadata: The metadata of this BackupBackupTargetStructGET.  # noqa: E501
        :type: object
        """

        self._metadata = metadata

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(BackupBackupTargetStructGET, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, BackupBackupTargetStructGET):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, BackupBackupTargetStructGET):
            return True

        return self.to_dict() != other.to_dict()
