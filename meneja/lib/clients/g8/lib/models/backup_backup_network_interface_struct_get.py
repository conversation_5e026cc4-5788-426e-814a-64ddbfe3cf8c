# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
    G8 API

    RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

    OpenAPI spec version: v4.0
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class BackupBackupNetworkInterfaceStructGET(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        "mac_address": "str",
        "ip_address": "str",
        "model": "str",
        "pci_address": "BackupBackupNetworkInterfaceStructGETPciAddress",
        "type": "str",
    }

    attribute_map = {
        "mac_address": "mac_address",
        "ip_address": "ip_address",
        "model": "model",
        "pci_address": "pci_address",
        "type": "type",
    }

    def __init__(
        self, mac_address=None, ip_address=None, model=None, pci_address=None, type=None, _configuration=None
    ):  # noqa: E501
        """BackupBackupNetworkInterfaceStructGET - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._mac_address = None
        self._ip_address = None
        self._model = None
        self._pci_address = None
        self._type = None
        self.discriminator = None

        self.mac_address = mac_address
        self.ip_address = ip_address
        self.model = model
        self.pci_address = pci_address
        self.type = type

    @property
    def mac_address(self):
        """Gets the mac_address of this BackupBackupNetworkInterfaceStructGET.  # noqa: E501

        default  # noqa: E501

        :return: The mac_address of this BackupBackupNetworkInterfaceStructGET.  # noqa: E501
        :rtype: str
        """
        return self._mac_address

    @mac_address.setter
    def mac_address(self, mac_address):
        """Sets the mac_address of this BackupBackupNetworkInterfaceStructGET.

        default  # noqa: E501

        :param mac_address: The mac_address of this BackupBackupNetworkInterfaceStructGET.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and mac_address is None:
            raise ValueError("Invalid value for `mac_address`, must not be `None`")  # noqa: E501

        self._mac_address = mac_address

    @property
    def ip_address(self):
        """Gets the ip_address of this BackupBackupNetworkInterfaceStructGET.  # noqa: E501

        default  # noqa: E501

        :return: The ip_address of this BackupBackupNetworkInterfaceStructGET.  # noqa: E501
        :rtype: str
        """
        return self._ip_address

    @ip_address.setter
    def ip_address(self, ip_address):
        """Sets the ip_address of this BackupBackupNetworkInterfaceStructGET.

        default  # noqa: E501

        :param ip_address: The ip_address of this BackupBackupNetworkInterfaceStructGET.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and ip_address is None:
            raise ValueError("Invalid value for `ip_address`, must not be `None`")  # noqa: E501

        self._ip_address = ip_address

    @property
    def model(self):
        """Gets the model of this BackupBackupNetworkInterfaceStructGET.  # noqa: E501

        default  # noqa: E501

        :return: The model of this BackupBackupNetworkInterfaceStructGET.  # noqa: E501
        :rtype: str
        """
        return self._model

    @model.setter
    def model(self, model):
        """Sets the model of this BackupBackupNetworkInterfaceStructGET.

        default  # noqa: E501

        :param model: The model of this BackupBackupNetworkInterfaceStructGET.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and model is None:
            raise ValueError("Invalid value for `model`, must not be `None`")  # noqa: E501

        self._model = model

    @property
    def pci_address(self):
        """Gets the pci_address of this BackupBackupNetworkInterfaceStructGET.  # noqa: E501


        :return: The pci_address of this BackupBackupNetworkInterfaceStructGET.  # noqa: E501
        :rtype: BackupBackupNetworkInterfaceStructGETPciAddress
        """
        return self._pci_address

    @pci_address.setter
    def pci_address(self, pci_address):
        """Sets the pci_address of this BackupBackupNetworkInterfaceStructGET.


        :param pci_address: The pci_address of this BackupBackupNetworkInterfaceStructGET.  # noqa: E501
        :type: BackupBackupNetworkInterfaceStructGETPciAddress
        """
        if self._configuration.client_side_validation and pci_address is None:
            raise ValueError("Invalid value for `pci_address`, must not be `None`")  # noqa: E501

        self._pci_address = pci_address

    @property
    def type(self):
        """Gets the type of this BackupBackupNetworkInterfaceStructGET.  # noqa: E501

        default  # noqa: E501

        :return: The type of this BackupBackupNetworkInterfaceStructGET.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this BackupBackupNetworkInterfaceStructGET.

        default  # noqa: E501

        :param type: The type of this BackupBackupNetworkInterfaceStructGET.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and type is None:
            raise ValueError("Invalid value for `type`, must not be `None`")  # noqa: E501

        self._type = type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(BackupBackupNetworkInterfaceStructGET, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, BackupBackupNetworkInterfaceStructGET):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, BackupBackupNetworkInterfaceStructGET):
            return True

        return self.to_dict() != other.to_dict()
