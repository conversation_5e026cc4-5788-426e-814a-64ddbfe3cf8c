# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
    G8 API

    RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

    OpenAPI spec version: v4.0
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class BackupBackupImageStructLIST(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {"id": "int", "os": "str", "os_type": "str", "boot_type": "str"}

    attribute_map = {"id": "id", "os": "os", "os_type": "os_type", "boot_type": "boot_type"}

    def __init__(self, id=None, os=None, os_type=None, boot_type=None, _configuration=None):  # noqa: E501
        """BackupBackupImageStructLIST - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._id = None
        self._os = None
        self._os_type = None
        self._boot_type = None
        self.discriminator = None

        self.id = id
        self.os = os
        self.os_type = os_type
        self.boot_type = boot_type

    @property
    def id(self):
        """Gets the id of this BackupBackupImageStructLIST.  # noqa: E501

        default  # noqa: E501

        :return: The id of this BackupBackupImageStructLIST.  # noqa: E501
        :rtype: int
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this BackupBackupImageStructLIST.

        default  # noqa: E501

        :param id: The id of this BackupBackupImageStructLIST.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and id is None:
            raise ValueError("Invalid value for `id`, must not be `None`")  # noqa: E501

        self._id = id

    @property
    def os(self):
        """Gets the os of this BackupBackupImageStructLIST.  # noqa: E501

        default  # noqa: E501

        :return: The os of this BackupBackupImageStructLIST.  # noqa: E501
        :rtype: str
        """
        return self._os

    @os.setter
    def os(self, os):
        """Sets the os of this BackupBackupImageStructLIST.

        default  # noqa: E501

        :param os: The os of this BackupBackupImageStructLIST.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and os is None:
            raise ValueError("Invalid value for `os`, must not be `None`")  # noqa: E501

        self._os = os

    @property
    def os_type(self):
        """Gets the os_type of this BackupBackupImageStructLIST.  # noqa: E501

        default  # noqa: E501

        :return: The os_type of this BackupBackupImageStructLIST.  # noqa: E501
        :rtype: str
        """
        return self._os_type

    @os_type.setter
    def os_type(self, os_type):
        """Sets the os_type of this BackupBackupImageStructLIST.

        default  # noqa: E501

        :param os_type: The os_type of this BackupBackupImageStructLIST.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and os_type is None:
            raise ValueError("Invalid value for `os_type`, must not be `None`")  # noqa: E501

        self._os_type = os_type

    @property
    def boot_type(self):
        """Gets the boot_type of this BackupBackupImageStructLIST.  # noqa: E501

        default  # noqa: E501

        :return: The boot_type of this BackupBackupImageStructLIST.  # noqa: E501
        :rtype: str
        """
        return self._boot_type

    @boot_type.setter
    def boot_type(self, boot_type):
        """Sets the boot_type of this BackupBackupImageStructLIST.

        default  # noqa: E501

        :param boot_type: The boot_type of this BackupBackupImageStructLIST.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and boot_type is None:
            raise ValueError("Invalid value for `boot_type`, must not be `None`")  # noqa: E501

        self._boot_type = boot_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(BackupBackupImageStructLIST, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, BackupBackupImageStructLIST):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, BackupBackupImageStructLIST):
            return True

        return self.to_dict() != other.to_dict()
