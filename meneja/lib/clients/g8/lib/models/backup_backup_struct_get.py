# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
    G8 API

    RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

    OpenAPI spec version: v4.0
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class BackupBackupStructGET(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        "id": "str",
        "policy": "int",
        "target": "int",
        "policy_name": "str",
        "vm": "BackupBackupStructGETVm",
        "location": "str",
        "start_time": "int",
        "end_time": "int",
        "snapshot_time": "int",
        "snapshot_end_time": "int",
        "origin": "str",
        "status": "str",
        "snapshot_status": "str",
        "failure_reason": "str",
        "failure_description": "str",
        "previous_backup": "str",
        "data_size": "int",
        "delta_size": "int",
        "workflow": "str",
        "account_id": "int",
        "account_name": "str",
        "cloudspace_id": "int",
        "cloudspace_name": "str",
        "metadata": "object",
        "warnings": "list[str]",
        "creation_timestamp": "int",
    }

    attribute_map = {
        "id": "id",
        "policy": "policy",
        "target": "target",
        "policy_name": "policy_name",
        "vm": "vm",
        "location": "location",
        "start_time": "start_time",
        "end_time": "end_time",
        "snapshot_time": "snapshot_time",
        "snapshot_end_time": "snapshot_end_time",
        "origin": "origin",
        "status": "status",
        "snapshot_status": "snapshot_status",
        "failure_reason": "failure_reason",
        "failure_description": "failure_description",
        "previous_backup": "previous_backup",
        "data_size": "data_size",
        "delta_size": "delta_size",
        "workflow": "workflow",
        "account_id": "account_id",
        "account_name": "account_name",
        "cloudspace_id": "cloudspace_id",
        "cloudspace_name": "cloudspace_name",
        "metadata": "metadata",
        "warnings": "warnings",
        "creation_timestamp": "creation_timestamp",
    }

    def __init__(
        self,
        id=None,
        policy=None,
        target=None,
        policy_name=None,
        vm=None,
        location=None,
        start_time=None,
        end_time=None,
        snapshot_time=None,
        snapshot_end_time=None,
        origin=None,
        status=None,
        snapshot_status=None,
        failure_reason=None,
        failure_description=None,
        previous_backup=None,
        data_size=None,
        delta_size=None,
        workflow=None,
        account_id=None,
        account_name=None,
        cloudspace_id=None,
        cloudspace_name=None,
        metadata=None,
        warnings=None,
        creation_timestamp=None,
        _configuration=None,
    ):  # noqa: E501
        """BackupBackupStructGET - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._id = None
        self._policy = None
        self._target = None
        self._policy_name = None
        self._vm = None
        self._location = None
        self._start_time = None
        self._end_time = None
        self._snapshot_time = None
        self._snapshot_end_time = None
        self._origin = None
        self._status = None
        self._snapshot_status = None
        self._failure_reason = None
        self._failure_description = None
        self._previous_backup = None
        self._data_size = None
        self._delta_size = None
        self._workflow = None
        self._account_id = None
        self._account_name = None
        self._cloudspace_id = None
        self._cloudspace_name = None
        self._metadata = None
        self._warnings = None
        self._creation_timestamp = None
        self.discriminator = None

        if id is not None:
            self.id = id
        if policy is not None:
            self.policy = policy
        if target is not None:
            self.target = target
        if policy_name is not None:
            self.policy_name = policy_name
        if vm is not None:
            self.vm = vm
        if location is not None:
            self.location = location
        if start_time is not None:
            self.start_time = start_time
        if end_time is not None:
            self.end_time = end_time
        if snapshot_time is not None:
            self.snapshot_time = snapshot_time
        if snapshot_end_time is not None:
            self.snapshot_end_time = snapshot_end_time
        if origin is not None:
            self.origin = origin
        if status is not None:
            self.status = status
        if snapshot_status is not None:
            self.snapshot_status = snapshot_status
        if failure_reason is not None:
            self.failure_reason = failure_reason
        if failure_description is not None:
            self.failure_description = failure_description
        if previous_backup is not None:
            self.previous_backup = previous_backup
        if data_size is not None:
            self.data_size = data_size
        if delta_size is not None:
            self.delta_size = delta_size
        if workflow is not None:
            self.workflow = workflow
        if account_id is not None:
            self.account_id = account_id
        if account_name is not None:
            self.account_name = account_name
        if cloudspace_id is not None:
            self.cloudspace_id = cloudspace_id
        if cloudspace_name is not None:
            self.cloudspace_name = cloudspace_name
        if metadata is not None:
            self.metadata = metadata
        if warnings is not None:
            self.warnings = warnings
        if creation_timestamp is not None:
            self.creation_timestamp = creation_timestamp

    @property
    def id(self):
        """Gets the id of this BackupBackupStructGET.  # noqa: E501

        resource id  # noqa: E501

        :return: The id of this BackupBackupStructGET.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this BackupBackupStructGET.

        resource id  # noqa: E501

        :param id: The id of this BackupBackupStructGET.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def policy(self):
        """Gets the policy of this BackupBackupStructGET.  # noqa: E501

        Backup policy id  # noqa: E501

        :return: The policy of this BackupBackupStructGET.  # noqa: E501
        :rtype: int
        """
        return self._policy

    @policy.setter
    def policy(self, policy):
        """Sets the policy of this BackupBackupStructGET.

        Backup policy id  # noqa: E501

        :param policy: The policy of this BackupBackupStructGET.  # noqa: E501
        :type: int
        """

        self._policy = policy

    @property
    def target(self):
        """Gets the target of this BackupBackupStructGET.  # noqa: E501

        Backup target id  # noqa: E501

        :return: The target of this BackupBackupStructGET.  # noqa: E501
        :rtype: int
        """
        return self._target

    @target.setter
    def target(self, target):
        """Sets the target of this BackupBackupStructGET.

        Backup target id  # noqa: E501

        :param target: The target of this BackupBackupStructGET.  # noqa: E501
        :type: int
        """

        self._target = target

    @property
    def policy_name(self):
        """Gets the policy_name of this BackupBackupStructGET.  # noqa: E501

        policy name  # noqa: E501

        :return: The policy_name of this BackupBackupStructGET.  # noqa: E501
        :rtype: str
        """
        return self._policy_name

    @policy_name.setter
    def policy_name(self, policy_name):
        """Sets the policy_name of this BackupBackupStructGET.

        policy name  # noqa: E501

        :param policy_name: The policy_name of this BackupBackupStructGET.  # noqa: E501
        :type: str
        """

        self._policy_name = policy_name

    @property
    def vm(self):
        """Gets the vm of this BackupBackupStructGET.  # noqa: E501


        :return: The vm of this BackupBackupStructGET.  # noqa: E501
        :rtype: BackupBackupStructGETVm
        """
        return self._vm

    @vm.setter
    def vm(self, vm):
        """Sets the vm of this BackupBackupStructGET.


        :param vm: The vm of this BackupBackupStructGET.  # noqa: E501
        :type: BackupBackupStructGETVm
        """

        self._vm = vm

    @property
    def location(self):
        """Gets the location of this BackupBackupStructGET.  # noqa: E501

        backup location  # noqa: E501

        :return: The location of this BackupBackupStructGET.  # noqa: E501
        :rtype: str
        """
        return self._location

    @location.setter
    def location(self, location):
        """Sets the location of this BackupBackupStructGET.

        backup location  # noqa: E501

        :param location: The location of this BackupBackupStructGET.  # noqa: E501
        :type: str
        """

        self._location = location

    @property
    def start_time(self):
        """Gets the start_time of this BackupBackupStructGET.  # noqa: E501

        When the backup operation is started  # noqa: E501

        :return: The start_time of this BackupBackupStructGET.  # noqa: E501
        :rtype: int
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this BackupBackupStructGET.

        When the backup operation is started  # noqa: E501

        :param start_time: The start_time of this BackupBackupStructGET.  # noqa: E501
        :type: int
        """

        self._start_time = start_time

    @property
    def end_time(self):
        """Gets the end_time of this BackupBackupStructGET.  # noqa: E501

        When the backup operation finishes  # noqa: E501

        :return: The end_time of this BackupBackupStructGET.  # noqa: E501
        :rtype: int
        """
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        """Sets the end_time of this BackupBackupStructGET.

        When the backup operation finishes  # noqa: E501

        :param end_time: The end_time of this BackupBackupStructGET.  # noqa: E501
        :type: int
        """

        self._end_time = end_time

    @property
    def snapshot_time(self):
        """Gets the snapshot_time of this BackupBackupStructGET.  # noqa: E501

        Time when the snapshot was specifically taken on the block devices  # noqa: E501

        :return: The snapshot_time of this BackupBackupStructGET.  # noqa: E501
        :rtype: int
        """
        return self._snapshot_time

    @snapshot_time.setter
    def snapshot_time(self, snapshot_time):
        """Sets the snapshot_time of this BackupBackupStructGET.

        Time when the snapshot was specifically taken on the block devices  # noqa: E501

        :param snapshot_time: The snapshot_time of this BackupBackupStructGET.  # noqa: E501
        :type: int
        """

        self._snapshot_time = snapshot_time

    @property
    def snapshot_end_time(self):
        """Gets the snapshot_end_time of this BackupBackupStructGET.  # noqa: E501

        Time when the snapshot was successfully taken  # noqa: E501

        :return: The snapshot_end_time of this BackupBackupStructGET.  # noqa: E501
        :rtype: int
        """
        return self._snapshot_end_time

    @snapshot_end_time.setter
    def snapshot_end_time(self, snapshot_end_time):
        """Sets the snapshot_end_time of this BackupBackupStructGET.

        Time when the snapshot was successfully taken  # noqa: E501

        :param snapshot_end_time: The snapshot_end_time of this BackupBackupStructGET.  # noqa: E501
        :type: int
        """

        self._snapshot_end_time = snapshot_end_time

    @property
    def origin(self):
        """Gets the origin of this BackupBackupStructGET.  # noqa: E501

        Defines who started the backup procedure  # noqa: E501

        :return: The origin of this BackupBackupStructGET.  # noqa: E501
        :rtype: str
        """
        return self._origin

    @origin.setter
    def origin(self, origin):
        """Sets the origin of this BackupBackupStructGET.

        Defines who started the backup procedure  # noqa: E501

        :param origin: The origin of this BackupBackupStructGET.  # noqa: E501
        :type: str
        """
        allowed_values = ["USER", "POLICY"]  # noqa: E501
        if self._configuration.client_side_validation and origin not in allowed_values:
            raise ValueError(
                "Invalid value for `origin` ({0}), must be one of {1}".format(origin, allowed_values)  # noqa: E501
            )

        self._origin = origin

    @property
    def status(self):
        """Gets the status of this BackupBackupStructGET.  # noqa: E501

        Backup status  # noqa: E501

        :return: The status of this BackupBackupStructGET.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this BackupBackupStructGET.

        Backup status  # noqa: E501

        :param status: The status of this BackupBackupStructGET.  # noqa: E501
        :type: str
        """
        allowed_values = [
            "MODELED",
            "QUEUED",
            "SNAPSHOTTING",
            "RUNNING",
            "EXPIRED",
            "SUCCEEDED",
            "FAILED",
            "SKIPPED",
            "QUEUED_FOR_BACKUP",
        ]  # noqa: E501
        if self._configuration.client_side_validation and status not in allowed_values:
            raise ValueError(
                "Invalid value for `status` ({0}), must be one of {1}".format(status, allowed_values)  # noqa: E501
            )

        self._status = status

    @property
    def snapshot_status(self):
        """Gets the snapshot_status of this BackupBackupStructGET.  # noqa: E501

        Backup snapshot status  # noqa: E501

        :return: The snapshot_status of this BackupBackupStructGET.  # noqa: E501
        :rtype: str
        """
        return self._snapshot_status

    @snapshot_status.setter
    def snapshot_status(self, snapshot_status):
        """Sets the snapshot_status of this BackupBackupStructGET.

        Backup snapshot status  # noqa: E501

        :param snapshot_status: The snapshot_status of this BackupBackupStructGET.  # noqa: E501
        :type: str
        """
        allowed_values = ["RAW", "CLEAN"]  # noqa: E501
        if self._configuration.client_side_validation and snapshot_status not in allowed_values:
            raise ValueError(
                "Invalid value for `snapshot_status` ({0}), must be one of {1}".format(  # noqa: E501
                    snapshot_status, allowed_values
                )
            )

        self._snapshot_status = snapshot_status

    @property
    def failure_reason(self):
        """Gets the failure_reason of this BackupBackupStructGET.  # noqa: E501

        Defines who started the backup procedure  # noqa: E501

        :return: The failure_reason of this BackupBackupStructGET.  # noqa: E501
        :rtype: str
        """
        return self._failure_reason

    @failure_reason.setter
    def failure_reason(self, failure_reason):
        """Sets the failure_reason of this BackupBackupStructGET.

        Defines who started the backup procedure  # noqa: E501

        :param failure_reason: The failure_reason of this BackupBackupStructGET.  # noqa: E501
        :type: str
        """
        allowed_values = [
            "SNAPSHOT_FAILURE",
            "RESTIC_REPOSITORY_LOCKED",
            "RESTIC_REPOSITORY_NOT_REACHABLE",
            "UNKNOWN_FAILURE",
            "VM_AGENT_NOT_ENABLED",
            "VM_AGENT_NOT_RUNNING",
            "FREEZE_TIMEOUT",
            "SNAPSHOT_TIMEOUT",
            "SNAPSHOT_NOT_FOUND",
            "ANOTHER_BACKUP_IS_RUNNING",
        ]  # noqa: E501
        if self._configuration.client_side_validation and failure_reason not in allowed_values:
            raise ValueError(
                "Invalid value for `failure_reason` ({0}), must be one of {1}".format(  # noqa: E501
                    failure_reason, allowed_values
                )
            )

        self._failure_reason = failure_reason

    @property
    def failure_description(self):
        """Gets the failure_description of this BackupBackupStructGET.  # noqa: E501

        More detailed information to understand why a backup operation failed  # noqa: E501

        :return: The failure_description of this BackupBackupStructGET.  # noqa: E501
        :rtype: str
        """
        return self._failure_description

    @failure_description.setter
    def failure_description(self, failure_description):
        """Sets the failure_description of this BackupBackupStructGET.

        More detailed information to understand why a backup operation failed  # noqa: E501

        :param failure_description: The failure_description of this BackupBackupStructGET.  # noqa: E501
        :type: str
        """

        self._failure_description = failure_description

    @property
    def previous_backup(self):
        """Gets the previous_backup of this BackupBackupStructGET.  # noqa: E501

        Id of the previous backup  # noqa: E501

        :return: The previous_backup of this BackupBackupStructGET.  # noqa: E501
        :rtype: str
        """
        return self._previous_backup

    @previous_backup.setter
    def previous_backup(self, previous_backup):
        """Sets the previous_backup of this BackupBackupStructGET.

        Id of the previous backup  # noqa: E501

        :param previous_backup: The previous_backup of this BackupBackupStructGET.  # noqa: E501
        :type: str
        """

        self._previous_backup = previous_backup

    @property
    def data_size(self):
        """Gets the data_size of this BackupBackupStructGET.  # noqa: E501

        Total backup size in bytes in the backup repository  # noqa: E501

        :return: The data_size of this BackupBackupStructGET.  # noqa: E501
        :rtype: int
        """
        return self._data_size

    @data_size.setter
    def data_size(self, data_size):
        """Sets the data_size of this BackupBackupStructGET.

        Total backup size in bytes in the backup repository  # noqa: E501

        :param data_size: The data_size of this BackupBackupStructGET.  # noqa: E501
        :type: int
        """

        self._data_size = data_size

    @property
    def delta_size(self):
        """Gets the delta_size of this BackupBackupStructGET.  # noqa: E501

        Delta size in bytes that was added to the backup repository compared to the previous backup  # noqa: E501

        :return: The delta_size of this BackupBackupStructGET.  # noqa: E501
        :rtype: int
        """
        return self._delta_size

    @delta_size.setter
    def delta_size(self, delta_size):
        """Sets the delta_size of this BackupBackupStructGET.

        Delta size in bytes that was added to the backup repository compared to the previous backup  # noqa: E501

        :param delta_size: The delta_size of this BackupBackupStructGET.  # noqa: E501
        :type: int
        """

        self._delta_size = delta_size

    @property
    def workflow(self):
        """Gets the workflow of this BackupBackupStructGET.  # noqa: E501

        Root task id of the dynaqueue workflow that creates the backup  # noqa: E501

        :return: The workflow of this BackupBackupStructGET.  # noqa: E501
        :rtype: str
        """
        return self._workflow

    @workflow.setter
    def workflow(self, workflow):
        """Sets the workflow of this BackupBackupStructGET.

        Root task id of the dynaqueue workflow that creates the backup  # noqa: E501

        :param workflow: The workflow of this BackupBackupStructGET.  # noqa: E501
        :type: str
        """

        self._workflow = workflow

    @property
    def account_id(self):
        """Gets the account_id of this BackupBackupStructGET.  # noqa: E501

        account id  # noqa: E501

        :return: The account_id of this BackupBackupStructGET.  # noqa: E501
        :rtype: int
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this BackupBackupStructGET.

        account id  # noqa: E501

        :param account_id: The account_id of this BackupBackupStructGET.  # noqa: E501
        :type: int
        """

        self._account_id = account_id

    @property
    def account_name(self):
        """Gets the account_name of this BackupBackupStructGET.  # noqa: E501

        account name  # noqa: E501

        :return: The account_name of this BackupBackupStructGET.  # noqa: E501
        :rtype: str
        """
        return self._account_name

    @account_name.setter
    def account_name(self, account_name):
        """Sets the account_name of this BackupBackupStructGET.

        account name  # noqa: E501

        :param account_name: The account_name of this BackupBackupStructGET.  # noqa: E501
        :type: str
        """

        self._account_name = account_name

    @property
    def cloudspace_id(self):
        """Gets the cloudspace_id of this BackupBackupStructGET.  # noqa: E501

        cloudspace id  # noqa: E501

        :return: The cloudspace_id of this BackupBackupStructGET.  # noqa: E501
        :rtype: int
        """
        return self._cloudspace_id

    @cloudspace_id.setter
    def cloudspace_id(self, cloudspace_id):
        """Sets the cloudspace_id of this BackupBackupStructGET.

        cloudspace id  # noqa: E501

        :param cloudspace_id: The cloudspace_id of this BackupBackupStructGET.  # noqa: E501
        :type: int
        """

        self._cloudspace_id = cloudspace_id

    @property
    def cloudspace_name(self):
        """Gets the cloudspace_name of this BackupBackupStructGET.  # noqa: E501

        cloudspace name  # noqa: E501

        :return: The cloudspace_name of this BackupBackupStructGET.  # noqa: E501
        :rtype: str
        """
        return self._cloudspace_name

    @cloudspace_name.setter
    def cloudspace_name(self, cloudspace_name):
        """Sets the cloudspace_name of this BackupBackupStructGET.

        cloudspace name  # noqa: E501

        :param cloudspace_name: The cloudspace_name of this BackupBackupStructGET.  # noqa: E501
        :type: str
        """

        self._cloudspace_name = cloudspace_name

    @property
    def metadata(self):
        """Gets the metadata of this BackupBackupStructGET.  # noqa: E501

        metadata  # noqa: E501

        :return: The metadata of this BackupBackupStructGET.  # noqa: E501
        :rtype: object
        """
        return self._metadata

    @metadata.setter
    def metadata(self, metadata):
        """Sets the metadata of this BackupBackupStructGET.

        metadata  # noqa: E501

        :param metadata: The metadata of this BackupBackupStructGET.  # noqa: E501
        :type: object
        """

        self._metadata = metadata

    @property
    def warnings(self):
        """Gets the warnings of this BackupBackupStructGET.  # noqa: E501

        list of warning messages  # noqa: E501

        :return: The warnings of this BackupBackupStructGET.  # noqa: E501
        :rtype: list[str]
        """
        return self._warnings

    @warnings.setter
    def warnings(self, warnings):
        """Sets the warnings of this BackupBackupStructGET.

        list of warning messages  # noqa: E501

        :param warnings: The warnings of this BackupBackupStructGET.  # noqa: E501
        :type: list[str]
        """

        self._warnings = warnings

    @property
    def creation_timestamp(self):
        """Gets the creation_timestamp of this BackupBackupStructGET.  # noqa: E501

        Epoch for backup creation timestamp  # noqa: E501

        :return: The creation_timestamp of this BackupBackupStructGET.  # noqa: E501
        :rtype: int
        """
        return self._creation_timestamp

    @creation_timestamp.setter
    def creation_timestamp(self, creation_timestamp):
        """Sets the creation_timestamp of this BackupBackupStructGET.

        Epoch for backup creation timestamp  # noqa: E501

        :param creation_timestamp: The creation_timestamp of this BackupBackupStructGET.  # noqa: E501
        :type: int
        """

        self._creation_timestamp = creation_timestamp

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(BackupBackupStructGET, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, BackupBackupStructGET):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, BackupBackupStructGET):
            return True

        return self.to_dict() != other.to_dict()
