# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
    G8 API

    RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

    OpenAPI spec version: v4.0
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class BackupBackupVMStructGET(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        "id": "int",
        "reference_id": "str",
        "name": "str",
        "description": "str",
        "hostname": "str",
        "memory": "int",
        "vcpus": "int",
        "boot_disk_id": "int",
        "disks": "list[BackupBackupDiskStructGET]",
        "network_interfaces": "list[BackupBackupNetworkInterfaceStructGET]",
        "image": "BackupBackupNetworkInterfaceStructGETPciAddress",
        "metadata": "object",
        "userdata": "object",
        "vtpm_restic_snapshot_id": "str",
    }

    attribute_map = {
        "id": "id",
        "reference_id": "reference_id",
        "name": "name",
        "description": "description",
        "hostname": "hostname",
        "memory": "memory",
        "vcpus": "vcpus",
        "boot_disk_id": "boot_disk_id",
        "disks": "disks",
        "network_interfaces": "network_interfaces",
        "image": "image",
        "metadata": "metadata",
        "userdata": "userdata",
        "vtpm_restic_snapshot_id": "vtpm_restic_snapshot_id",
    }

    def __init__(
        self,
        id=None,
        reference_id=None,
        name=None,
        description=None,
        hostname=None,
        memory=None,
        vcpus=None,
        boot_disk_id=None,
        disks=None,
        network_interfaces=None,
        image=None,
        metadata=None,
        userdata=None,
        vtpm_restic_snapshot_id=None,
        _configuration=None,
    ):  # noqa: E501
        """BackupBackupVMStructGET - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._id = None
        self._reference_id = None
        self._name = None
        self._description = None
        self._hostname = None
        self._memory = None
        self._vcpus = None
        self._boot_disk_id = None
        self._disks = None
        self._network_interfaces = None
        self._image = None
        self._metadata = None
        self._userdata = None
        self._vtpm_restic_snapshot_id = None
        self.discriminator = None

        self.id = id
        self.reference_id = reference_id
        if name is not None:
            self.name = name
        if description is not None:
            self.description = description
        if hostname is not None:
            self.hostname = hostname
        self.memory = memory
        self.vcpus = vcpus
        self.boot_disk_id = boot_disk_id
        self.disks = disks
        self.network_interfaces = network_interfaces
        self.image = image
        if metadata is not None:
            self.metadata = metadata
        if userdata is not None:
            self.userdata = userdata
        if vtpm_restic_snapshot_id is not None:
            self.vtpm_restic_snapshot_id = vtpm_restic_snapshot_id

    @property
    def id(self):
        """Gets the id of this BackupBackupVMStructGET.  # noqa: E501

        default  # noqa: E501

        :return: The id of this BackupBackupVMStructGET.  # noqa: E501
        :rtype: int
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this BackupBackupVMStructGET.

        default  # noqa: E501

        :param id: The id of this BackupBackupVMStructGET.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and id is None:
            raise ValueError("Invalid value for `id`, must not be `None`")  # noqa: E501

        self._id = id

    @property
    def reference_id(self):
        """Gets the reference_id of this BackupBackupVMStructGET.  # noqa: E501

        default  # noqa: E501

        :return: The reference_id of this BackupBackupVMStructGET.  # noqa: E501
        :rtype: str
        """
        return self._reference_id

    @reference_id.setter
    def reference_id(self, reference_id):
        """Sets the reference_id of this BackupBackupVMStructGET.

        default  # noqa: E501

        :param reference_id: The reference_id of this BackupBackupVMStructGET.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and reference_id is None:
            raise ValueError("Invalid value for `reference_id`, must not be `None`")  # noqa: E501

        self._reference_id = reference_id

    @property
    def name(self):
        """Gets the name of this BackupBackupVMStructGET.  # noqa: E501

        default  # noqa: E501

        :return: The name of this BackupBackupVMStructGET.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this BackupBackupVMStructGET.

        default  # noqa: E501

        :param name: The name of this BackupBackupVMStructGET.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def description(self):
        """Gets the description of this BackupBackupVMStructGET.  # noqa: E501

        default  # noqa: E501

        :return: The description of this BackupBackupVMStructGET.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this BackupBackupVMStructGET.

        default  # noqa: E501

        :param description: The description of this BackupBackupVMStructGET.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def hostname(self):
        """Gets the hostname of this BackupBackupVMStructGET.  # noqa: E501

        default  # noqa: E501

        :return: The hostname of this BackupBackupVMStructGET.  # noqa: E501
        :rtype: str
        """
        return self._hostname

    @hostname.setter
    def hostname(self, hostname):
        """Sets the hostname of this BackupBackupVMStructGET.

        default  # noqa: E501

        :param hostname: The hostname of this BackupBackupVMStructGET.  # noqa: E501
        :type: str
        """

        self._hostname = hostname

    @property
    def memory(self):
        """Gets the memory of this BackupBackupVMStructGET.  # noqa: E501

        default  # noqa: E501

        :return: The memory of this BackupBackupVMStructGET.  # noqa: E501
        :rtype: int
        """
        return self._memory

    @memory.setter
    def memory(self, memory):
        """Sets the memory of this BackupBackupVMStructGET.

        default  # noqa: E501

        :param memory: The memory of this BackupBackupVMStructGET.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and memory is None:
            raise ValueError("Invalid value for `memory`, must not be `None`")  # noqa: E501

        self._memory = memory

    @property
    def vcpus(self):
        """Gets the vcpus of this BackupBackupVMStructGET.  # noqa: E501

        default  # noqa: E501

        :return: The vcpus of this BackupBackupVMStructGET.  # noqa: E501
        :rtype: int
        """
        return self._vcpus

    @vcpus.setter
    def vcpus(self, vcpus):
        """Sets the vcpus of this BackupBackupVMStructGET.

        default  # noqa: E501

        :param vcpus: The vcpus of this BackupBackupVMStructGET.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and vcpus is None:
            raise ValueError("Invalid value for `vcpus`, must not be `None`")  # noqa: E501

        self._vcpus = vcpus

    @property
    def boot_disk_id(self):
        """Gets the boot_disk_id of this BackupBackupVMStructGET.  # noqa: E501

        default  # noqa: E501

        :return: The boot_disk_id of this BackupBackupVMStructGET.  # noqa: E501
        :rtype: int
        """
        return self._boot_disk_id

    @boot_disk_id.setter
    def boot_disk_id(self, boot_disk_id):
        """Sets the boot_disk_id of this BackupBackupVMStructGET.

        default  # noqa: E501

        :param boot_disk_id: The boot_disk_id of this BackupBackupVMStructGET.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and boot_disk_id is None:
            raise ValueError("Invalid value for `boot_disk_id`, must not be `None`")  # noqa: E501

        self._boot_disk_id = boot_disk_id

    @property
    def disks(self):
        """Gets the disks of this BackupBackupVMStructGET.  # noqa: E501

        default  # noqa: E501

        :return: The disks of this BackupBackupVMStructGET.  # noqa: E501
        :rtype: list[BackupBackupDiskStructGET]
        """
        return self._disks

    @disks.setter
    def disks(self, disks):
        """Sets the disks of this BackupBackupVMStructGET.

        default  # noqa: E501

        :param disks: The disks of this BackupBackupVMStructGET.  # noqa: E501
        :type: list[BackupBackupDiskStructGET]
        """
        if self._configuration.client_side_validation and disks is None:
            raise ValueError("Invalid value for `disks`, must not be `None`")  # noqa: E501

        self._disks = disks

    @property
    def network_interfaces(self):
        """Gets the network_interfaces of this BackupBackupVMStructGET.  # noqa: E501

        default  # noqa: E501

        :return: The network_interfaces of this BackupBackupVMStructGET.  # noqa: E501
        :rtype: list[BackupBackupNetworkInterfaceStructGET]
        """
        return self._network_interfaces

    @network_interfaces.setter
    def network_interfaces(self, network_interfaces):
        """Sets the network_interfaces of this BackupBackupVMStructGET.

        default  # noqa: E501

        :param network_interfaces: The network_interfaces of this BackupBackupVMStructGET.  # noqa: E501
        :type: list[BackupBackupNetworkInterfaceStructGET]
        """
        if self._configuration.client_side_validation and network_interfaces is None:
            raise ValueError("Invalid value for `network_interfaces`, must not be `None`")  # noqa: E501

        self._network_interfaces = network_interfaces

    @property
    def image(self):
        """Gets the image of this BackupBackupVMStructGET.  # noqa: E501


        :return: The image of this BackupBackupVMStructGET.  # noqa: E501
        :rtype: BackupBackupNetworkInterfaceStructGETPciAddress
        """
        return self._image

    @image.setter
    def image(self, image):
        """Sets the image of this BackupBackupVMStructGET.


        :param image: The image of this BackupBackupVMStructGET.  # noqa: E501
        :type: BackupBackupNetworkInterfaceStructGETPciAddress
        """
        if self._configuration.client_side_validation and image is None:
            raise ValueError("Invalid value for `image`, must not be `None`")  # noqa: E501

        self._image = image

    @property
    def metadata(self):
        """Gets the metadata of this BackupBackupVMStructGET.  # noqa: E501

        default  # noqa: E501

        :return: The metadata of this BackupBackupVMStructGET.  # noqa: E501
        :rtype: object
        """
        return self._metadata

    @metadata.setter
    def metadata(self, metadata):
        """Sets the metadata of this BackupBackupVMStructGET.

        default  # noqa: E501

        :param metadata: The metadata of this BackupBackupVMStructGET.  # noqa: E501
        :type: object
        """

        self._metadata = metadata

    @property
    def userdata(self):
        """Gets the userdata of this BackupBackupVMStructGET.  # noqa: E501

        default  # noqa: E501

        :return: The userdata of this BackupBackupVMStructGET.  # noqa: E501
        :rtype: object
        """
        return self._userdata

    @userdata.setter
    def userdata(self, userdata):
        """Sets the userdata of this BackupBackupVMStructGET.

        default  # noqa: E501

        :param userdata: The userdata of this BackupBackupVMStructGET.  # noqa: E501
        :type: object
        """

        self._userdata = userdata

    @property
    def vtpm_restic_snapshot_id(self):
        """Gets the vtpm_restic_snapshot_id of this BackupBackupVMStructGET.  # noqa: E501

        Restic snapshot id of the vtpm state file  # noqa: E501

        :return: The vtpm_restic_snapshot_id of this BackupBackupVMStructGET.  # noqa: E501
        :rtype: str
        """
        return self._vtpm_restic_snapshot_id

    @vtpm_restic_snapshot_id.setter
    def vtpm_restic_snapshot_id(self, vtpm_restic_snapshot_id):
        """Sets the vtpm_restic_snapshot_id of this BackupBackupVMStructGET.

        Restic snapshot id of the vtpm state file  # noqa: E501

        :param vtpm_restic_snapshot_id: The vtpm_restic_snapshot_id of this BackupBackupVMStructGET.  # noqa: E501
        :type: str
        """

        self._vtpm_restic_snapshot_id = vtpm_restic_snapshot_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(BackupBackupVMStructGET, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, BackupBackupVMStructGET):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, BackupBackupVMStructGET):
            return True

        return self.to_dict() != other.to_dict()
