# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
    G8 API

    RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

    OpenAPI spec version: v4.0
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class BackupVMPCIAddressGET(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {"domain": "int", "bus": "int", "slot": "int", "function": "int", "guid": "str"}

    attribute_map = {"domain": "domain", "bus": "bus", "slot": "slot", "function": "function", "guid": "guid"}

    def __init__(self, domain=None, bus=None, slot=None, function=None, guid=None, _configuration=None):  # noqa: E501
        """BackupVMPCIAddressGET - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._domain = None
        self._bus = None
        self._slot = None
        self._function = None
        self._guid = None
        self.discriminator = None

        self.domain = domain
        self.bus = bus
        self.slot = slot
        self.function = function
        self.guid = guid

    @property
    def domain(self):
        """Gets the domain of this BackupVMPCIAddressGET.  # noqa: E501

        default  # noqa: E501

        :return: The domain of this BackupVMPCIAddressGET.  # noqa: E501
        :rtype: int
        """
        return self._domain

    @domain.setter
    def domain(self, domain):
        """Sets the domain of this BackupVMPCIAddressGET.

        default  # noqa: E501

        :param domain: The domain of this BackupVMPCIAddressGET.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and domain is None:
            raise ValueError("Invalid value for `domain`, must not be `None`")  # noqa: E501

        self._domain = domain

    @property
    def bus(self):
        """Gets the bus of this BackupVMPCIAddressGET.  # noqa: E501

        default  # noqa: E501

        :return: The bus of this BackupVMPCIAddressGET.  # noqa: E501
        :rtype: int
        """
        return self._bus

    @bus.setter
    def bus(self, bus):
        """Sets the bus of this BackupVMPCIAddressGET.

        default  # noqa: E501

        :param bus: The bus of this BackupVMPCIAddressGET.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and bus is None:
            raise ValueError("Invalid value for `bus`, must not be `None`")  # noqa: E501

        self._bus = bus

    @property
    def slot(self):
        """Gets the slot of this BackupVMPCIAddressGET.  # noqa: E501

        default  # noqa: E501

        :return: The slot of this BackupVMPCIAddressGET.  # noqa: E501
        :rtype: int
        """
        return self._slot

    @slot.setter
    def slot(self, slot):
        """Sets the slot of this BackupVMPCIAddressGET.

        default  # noqa: E501

        :param slot: The slot of this BackupVMPCIAddressGET.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and slot is None:
            raise ValueError("Invalid value for `slot`, must not be `None`")  # noqa: E501

        self._slot = slot

    @property
    def function(self):
        """Gets the function of this BackupVMPCIAddressGET.  # noqa: E501

        default  # noqa: E501

        :return: The function of this BackupVMPCIAddressGET.  # noqa: E501
        :rtype: int
        """
        return self._function

    @function.setter
    def function(self, function):
        """Sets the function of this BackupVMPCIAddressGET.

        default  # noqa: E501

        :param function: The function of this BackupVMPCIAddressGET.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and function is None:
            raise ValueError("Invalid value for `function`, must not be `None`")  # noqa: E501

        self._function = function

    @property
    def guid(self):
        """Gets the guid of this BackupVMPCIAddressGET.  # noqa: E501

        default  # noqa: E501

        :return: The guid of this BackupVMPCIAddressGET.  # noqa: E501
        :rtype: str
        """
        return self._guid

    @guid.setter
    def guid(self, guid):
        """Sets the guid of this BackupVMPCIAddressGET.

        default  # noqa: E501

        :param guid: The guid of this BackupVMPCIAddressGET.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and guid is None:
            raise ValueError("Invalid value for `guid`, must not be `None`")  # noqa: E501

        self._guid = guid

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(BackupVMPCIAddressGET, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, BackupVMPCIAddressGET):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, BackupVMPCIAddressGET):
            return True

        return self.to_dict() != other.to_dict()
