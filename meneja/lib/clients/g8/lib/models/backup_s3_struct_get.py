# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
    G8 API

    RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

    OpenAPI spec version: v4.0
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class BackupS3StructGET(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {"url": "str", "region": "str", "bucket": "str", "locking_mode": "str"}

    attribute_map = {"url": "url", "region": "region", "bucket": "bucket", "locking_mode": "locking_mode"}

    def __init__(self, url=None, region=None, bucket=None, locking_mode=None, _configuration=None):  # noqa: E501
        """BackupS3StructGET - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._url = None
        self._region = None
        self._bucket = None
        self._locking_mode = None
        self.discriminator = None

        self.url = url
        self.region = region
        self.bucket = bucket
        self.locking_mode = locking_mode

    @property
    def url(self):
        """Gets the url of this BackupS3StructGET.  # noqa: E501

        S3 url  # noqa: E501

        :return: The url of this BackupS3StructGET.  # noqa: E501
        :rtype: str
        """
        return self._url

    @url.setter
    def url(self, url):
        """Sets the url of this BackupS3StructGET.

        S3 url  # noqa: E501

        :param url: The url of this BackupS3StructGET.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and url is None:
            raise ValueError("Invalid value for `url`, must not be `None`")  # noqa: E501

        self._url = url

    @property
    def region(self):
        """Gets the region of this BackupS3StructGET.  # noqa: E501

        S3 region  # noqa: E501

        :return: The region of this BackupS3StructGET.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this BackupS3StructGET.

        S3 region  # noqa: E501

        :param region: The region of this BackupS3StructGET.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and region is None:
            raise ValueError("Invalid value for `region`, must not be `None`")  # noqa: E501

        self._region = region

    @property
    def bucket(self):
        """Gets the bucket of this BackupS3StructGET.  # noqa: E501

        S3 bucket  # noqa: E501

        :return: The bucket of this BackupS3StructGET.  # noqa: E501
        :rtype: str
        """
        return self._bucket

    @bucket.setter
    def bucket(self, bucket):
        """Sets the bucket of this BackupS3StructGET.

        S3 bucket  # noqa: E501

        :param bucket: The bucket of this BackupS3StructGET.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and bucket is None:
            raise ValueError("Invalid value for `bucket`, must not be `None`")  # noqa: E501

        self._bucket = bucket

    @property
    def locking_mode(self):
        """Gets the locking_mode of this BackupS3StructGET.  # noqa: E501

        S3 locking mode  # noqa: E501

        :return: The locking_mode of this BackupS3StructGET.  # noqa: E501
        :rtype: str
        """
        return self._locking_mode

    @locking_mode.setter
    def locking_mode(self, locking_mode):
        """Sets the locking_mode of this BackupS3StructGET.

        S3 locking mode  # noqa: E501

        :param locking_mode: The locking_mode of this BackupS3StructGET.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and locking_mode is None:
            raise ValueError("Invalid value for `locking_mode`, must not be `None`")  # noqa: E501
        allowed_values = ["NO_LOCKING", "GOVERNANCE", "COMPLIANCE"]  # noqa: E501
        if self._configuration.client_side_validation and locking_mode not in allowed_values:
            raise ValueError(
                "Invalid value for `locking_mode` ({0}), must be one of {1}".format(  # noqa: E501
                    locking_mode, allowed_values
                )
            )

        self._locking_mode = locking_mode

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(BackupS3StructGET, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, BackupS3StructGET):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, BackupS3StructGET):
            return True

        return self.to_dict() != other.to_dict()
