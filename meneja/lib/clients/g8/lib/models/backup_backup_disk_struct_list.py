# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
    G8 API

    RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

    OpenAPI spec version: v4.0
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class BackupBackupDiskStructLIST(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        "id": "int",
        "size": "int",
        "type": "str",
        "reference_id": "str",
        "host": "str",
        "port": "int",
        "backup_size": "int",
        "delta_size": "int",
        "restic_snapshot_id": "str",
        "restic_snapshot_path": "str",
        "snapshot_guid": "str",
        "clone_disk_guid": "str",
        "backup_blocksize": "int",
    }

    attribute_map = {
        "id": "id",
        "size": "size",
        "type": "type",
        "reference_id": "reference_id",
        "host": "host",
        "port": "port",
        "backup_size": "backup_size",
        "delta_size": "delta_size",
        "restic_snapshot_id": "restic_snapshot_id",
        "restic_snapshot_path": "restic_snapshot_path",
        "snapshot_guid": "snapshot_guid",
        "clone_disk_guid": "clone_disk_guid",
        "backup_blocksize": "backup_blocksize",
    }

    def __init__(
        self,
        id=None,
        size=None,
        type=None,
        reference_id=None,
        host=None,
        port=None,
        backup_size=None,
        delta_size=None,
        restic_snapshot_id=None,
        restic_snapshot_path=None,
        snapshot_guid=None,
        clone_disk_guid=None,
        backup_blocksize=None,
        _configuration=None,
    ):  # noqa: E501
        """BackupBackupDiskStructLIST - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._id = None
        self._size = None
        self._type = None
        self._reference_id = None
        self._host = None
        self._port = None
        self._backup_size = None
        self._delta_size = None
        self._restic_snapshot_id = None
        self._restic_snapshot_path = None
        self._snapshot_guid = None
        self._clone_disk_guid = None
        self._backup_blocksize = None
        self.discriminator = None

        self.id = id
        self.size = size
        self.type = type
        self.reference_id = reference_id
        if host is not None:
            self.host = host
        if port is not None:
            self.port = port
        if backup_size is not None:
            self.backup_size = backup_size
        if delta_size is not None:
            self.delta_size = delta_size
        if restic_snapshot_id is not None:
            self.restic_snapshot_id = restic_snapshot_id
        if restic_snapshot_path is not None:
            self.restic_snapshot_path = restic_snapshot_path
        if snapshot_guid is not None:
            self.snapshot_guid = snapshot_guid
        if clone_disk_guid is not None:
            self.clone_disk_guid = clone_disk_guid
        if backup_blocksize is not None:
            self.backup_blocksize = backup_blocksize

    @property
    def id(self):
        """Gets the id of this BackupBackupDiskStructLIST.  # noqa: E501

        default  # noqa: E501

        :return: The id of this BackupBackupDiskStructLIST.  # noqa: E501
        :rtype: int
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this BackupBackupDiskStructLIST.

        default  # noqa: E501

        :param id: The id of this BackupBackupDiskStructLIST.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and id is None:
            raise ValueError("Invalid value for `id`, must not be `None`")  # noqa: E501

        self._id = id

    @property
    def size(self):
        """Gets the size of this BackupBackupDiskStructLIST.  # noqa: E501

        default  # noqa: E501

        :return: The size of this BackupBackupDiskStructLIST.  # noqa: E501
        :rtype: int
        """
        return self._size

    @size.setter
    def size(self, size):
        """Sets the size of this BackupBackupDiskStructLIST.

        default  # noqa: E501

        :param size: The size of this BackupBackupDiskStructLIST.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and size is None:
            raise ValueError("Invalid value for `size`, must not be `None`")  # noqa: E501

        self._size = size

    @property
    def type(self):
        """Gets the type of this BackupBackupDiskStructLIST.  # noqa: E501

        default  # noqa: E501

        :return: The type of this BackupBackupDiskStructLIST.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this BackupBackupDiskStructLIST.

        default  # noqa: E501

        :param type: The type of this BackupBackupDiskStructLIST.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and type is None:
            raise ValueError("Invalid value for `type`, must not be `None`")  # noqa: E501

        self._type = type

    @property
    def reference_id(self):
        """Gets the reference_id of this BackupBackupDiskStructLIST.  # noqa: E501

        default  # noqa: E501

        :return: The reference_id of this BackupBackupDiskStructLIST.  # noqa: E501
        :rtype: str
        """
        return self._reference_id

    @reference_id.setter
    def reference_id(self, reference_id):
        """Sets the reference_id of this BackupBackupDiskStructLIST.

        default  # noqa: E501

        :param reference_id: The reference_id of this BackupBackupDiskStructLIST.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and reference_id is None:
            raise ValueError("Invalid value for `reference_id`, must not be `None`")  # noqa: E501

        self._reference_id = reference_id

    @property
    def host(self):
        """Gets the host of this BackupBackupDiskStructLIST.  # noqa: E501

        default  # noqa: E501

        :return: The host of this BackupBackupDiskStructLIST.  # noqa: E501
        :rtype: str
        """
        return self._host

    @host.setter
    def host(self, host):
        """Sets the host of this BackupBackupDiskStructLIST.

        default  # noqa: E501

        :param host: The host of this BackupBackupDiskStructLIST.  # noqa: E501
        :type: str
        """

        self._host = host

    @property
    def port(self):
        """Gets the port of this BackupBackupDiskStructLIST.  # noqa: E501

        default  # noqa: E501

        :return: The port of this BackupBackupDiskStructLIST.  # noqa: E501
        :rtype: int
        """
        return self._port

    @port.setter
    def port(self, port):
        """Sets the port of this BackupBackupDiskStructLIST.

        default  # noqa: E501

        :param port: The port of this BackupBackupDiskStructLIST.  # noqa: E501
        :type: int
        """

        self._port = port

    @property
    def backup_size(self):
        """Gets the backup_size of this BackupBackupDiskStructLIST.  # noqa: E501

        default  # noqa: E501

        :return: The backup_size of this BackupBackupDiskStructLIST.  # noqa: E501
        :rtype: int
        """
        return self._backup_size

    @backup_size.setter
    def backup_size(self, backup_size):
        """Sets the backup_size of this BackupBackupDiskStructLIST.

        default  # noqa: E501

        :param backup_size: The backup_size of this BackupBackupDiskStructLIST.  # noqa: E501
        :type: int
        """

        self._backup_size = backup_size

    @property
    def delta_size(self):
        """Gets the delta_size of this BackupBackupDiskStructLIST.  # noqa: E501

        default  # noqa: E501

        :return: The delta_size of this BackupBackupDiskStructLIST.  # noqa: E501
        :rtype: int
        """
        return self._delta_size

    @delta_size.setter
    def delta_size(self, delta_size):
        """Sets the delta_size of this BackupBackupDiskStructLIST.

        default  # noqa: E501

        :param delta_size: The delta_size of this BackupBackupDiskStructLIST.  # noqa: E501
        :type: int
        """

        self._delta_size = delta_size

    @property
    def restic_snapshot_id(self):
        """Gets the restic_snapshot_id of this BackupBackupDiskStructLIST.  # noqa: E501

        default  # noqa: E501

        :return: The restic_snapshot_id of this BackupBackupDiskStructLIST.  # noqa: E501
        :rtype: str
        """
        return self._restic_snapshot_id

    @restic_snapshot_id.setter
    def restic_snapshot_id(self, restic_snapshot_id):
        """Sets the restic_snapshot_id of this BackupBackupDiskStructLIST.

        default  # noqa: E501

        :param restic_snapshot_id: The restic_snapshot_id of this BackupBackupDiskStructLIST.  # noqa: E501
        :type: str
        """

        self._restic_snapshot_id = restic_snapshot_id

    @property
    def restic_snapshot_path(self):
        """Gets the restic_snapshot_path of this BackupBackupDiskStructLIST.  # noqa: E501

        default  # noqa: E501

        :return: The restic_snapshot_path of this BackupBackupDiskStructLIST.  # noqa: E501
        :rtype: str
        """
        return self._restic_snapshot_path

    @restic_snapshot_path.setter
    def restic_snapshot_path(self, restic_snapshot_path):
        """Sets the restic_snapshot_path of this BackupBackupDiskStructLIST.

        default  # noqa: E501

        :param restic_snapshot_path: The restic_snapshot_path of this BackupBackupDiskStructLIST.  # noqa: E501
        :type: str
        """

        self._restic_snapshot_path = restic_snapshot_path

    @property
    def snapshot_guid(self):
        """Gets the snapshot_guid of this BackupBackupDiskStructLIST.  # noqa: E501

        default  # noqa: E501

        :return: The snapshot_guid of this BackupBackupDiskStructLIST.  # noqa: E501
        :rtype: str
        """
        return self._snapshot_guid

    @snapshot_guid.setter
    def snapshot_guid(self, snapshot_guid):
        """Sets the snapshot_guid of this BackupBackupDiskStructLIST.

        default  # noqa: E501

        :param snapshot_guid: The snapshot_guid of this BackupBackupDiskStructLIST.  # noqa: E501
        :type: str
        """

        self._snapshot_guid = snapshot_guid

    @property
    def clone_disk_guid(self):
        """Gets the clone_disk_guid of this BackupBackupDiskStructLIST.  # noqa: E501

        default  # noqa: E501

        :return: The clone_disk_guid of this BackupBackupDiskStructLIST.  # noqa: E501
        :rtype: str
        """
        return self._clone_disk_guid

    @clone_disk_guid.setter
    def clone_disk_guid(self, clone_disk_guid):
        """Sets the clone_disk_guid of this BackupBackupDiskStructLIST.

        default  # noqa: E501

        :param clone_disk_guid: The clone_disk_guid of this BackupBackupDiskStructLIST.  # noqa: E501
        :type: str
        """

        self._clone_disk_guid = clone_disk_guid

    @property
    def backup_blocksize(self):
        """Gets the backup_blocksize of this BackupBackupDiskStructLIST.  # noqa: E501

        default  # noqa: E501

        :return: The backup_blocksize of this BackupBackupDiskStructLIST.  # noqa: E501
        :rtype: int
        """
        return self._backup_blocksize

    @backup_blocksize.setter
    def backup_blocksize(self, backup_blocksize):
        """Sets the backup_blocksize of this BackupBackupDiskStructLIST.

        default  # noqa: E501

        :param backup_blocksize: The backup_blocksize of this BackupBackupDiskStructLIST.  # noqa: E501
        :type: int
        """

        self._backup_blocksize = backup_blocksize

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(BackupBackupDiskStructLIST, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, BackupBackupDiskStructLIST):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, BackupBackupDiskStructLIST):
            return True

        return self.to_dict() != other.to_dict()
