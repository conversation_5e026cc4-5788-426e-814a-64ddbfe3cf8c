# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
    G8 API

    RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

    OpenAPI spec version: v4.0
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class BackupSnapshotPolicyGET(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        "cooperative": "bool",
        "cooperative_failure_behaviour": "str",
        "cooperative_timeout": "int",
        "timeout": "int",
        "retry_pause": "int",
        "retry_times": "int",
    }

    attribute_map = {
        "cooperative": "cooperative",
        "cooperative_failure_behaviour": "cooperative_failure_behaviour",
        "cooperative_timeout": "cooperative_timeout",
        "timeout": "timeout",
        "retry_pause": "retry_pause",
        "retry_times": "retry_times",
    }

    def __init__(
        self,
        cooperative=None,
        cooperative_failure_behaviour=None,
        cooperative_timeout=None,
        timeout=None,
        retry_pause=None,
        retry_times=None,
        _configuration=None,
    ):  # noqa: E501
        """BackupSnapshotPolicyGET - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cooperative = None
        self._cooperative_failure_behaviour = None
        self._cooperative_timeout = None
        self._timeout = None
        self._retry_pause = None
        self._retry_times = None
        self.discriminator = None

        self.cooperative = cooperative
        if cooperative_failure_behaviour is not None:
            self.cooperative_failure_behaviour = cooperative_failure_behaviour
        if cooperative_timeout is not None:
            self.cooperative_timeout = cooperative_timeout
        self.timeout = timeout
        if retry_pause is not None:
            self.retry_pause = retry_pause
        if retry_times is not None:
            self.retry_times = retry_times

    @property
    def cooperative(self):
        """Gets the cooperative of this BackupSnapshotPolicyGET.  # noqa: E501

        If set, it will freeze the VM's file systems during the snapshot operation  # noqa: E501

        :return: The cooperative of this BackupSnapshotPolicyGET.  # noqa: E501
        :rtype: bool
        """
        return self._cooperative

    @cooperative.setter
    def cooperative(self, cooperative):
        """Sets the cooperative of this BackupSnapshotPolicyGET.

        If set, it will freeze the VM's file systems during the snapshot operation  # noqa: E501

        :param cooperative: The cooperative of this BackupSnapshotPolicyGET.  # noqa: E501
        :type: bool
        """
        if self._configuration.client_side_validation and cooperative is None:
            raise ValueError("Invalid value for `cooperative`, must not be `None`")  # noqa: E501

        self._cooperative = cooperative

    @property
    def cooperative_failure_behaviour(self):
        """Gets the cooperative_failure_behaviour of this BackupSnapshotPolicyGET.  # noqa: E501

          # noqa: E501

        :return: The cooperative_failure_behaviour of this BackupSnapshotPolicyGET.  # noqa: E501
        :rtype: str
        """
        return self._cooperative_failure_behaviour

    @cooperative_failure_behaviour.setter
    def cooperative_failure_behaviour(self, cooperative_failure_behaviour):
        """Sets the cooperative_failure_behaviour of this BackupSnapshotPolicyGET.

          # noqa: E501

        :param cooperative_failure_behaviour: The cooperative_failure_behaviour of this BackupSnapshotPolicyGET.  # noqa: E501
        :type: str
        """
        allowed_values = ["FAIL", "RETRY_IF_SNAPSHOT_TIMEOUT", "CONTINUE_WITHOUT_COOPERATION"]  # noqa: E501
        if self._configuration.client_side_validation and cooperative_failure_behaviour not in allowed_values:
            raise ValueError(
                "Invalid value for `cooperative_failure_behaviour` ({0}), must be one of {1}".format(  # noqa: E501
                    cooperative_failure_behaviour, allowed_values
                )
            )

        self._cooperative_failure_behaviour = cooperative_failure_behaviour

    @property
    def cooperative_timeout(self):
        """Gets the cooperative_timeout of this BackupSnapshotPolicyGET.  # noqa: E501

        Number of seconds that freezing the VM's file system should take maximally  # noqa: E501

        :return: The cooperative_timeout of this BackupSnapshotPolicyGET.  # noqa: E501
        :rtype: int
        """
        return self._cooperative_timeout

    @cooperative_timeout.setter
    def cooperative_timeout(self, cooperative_timeout):
        """Sets the cooperative_timeout of this BackupSnapshotPolicyGET.

        Number of seconds that freezing the VM's file system should take maximally  # noqa: E501

        :param cooperative_timeout: The cooperative_timeout of this BackupSnapshotPolicyGET.  # noqa: E501
        :type: int
        """

        self._cooperative_timeout = cooperative_timeout

    @property
    def timeout(self):
        """Gets the timeout of this BackupSnapshotPolicyGET.  # noqa: E501

        Maximum number of seconds to wait on the snapshot creation  # noqa: E501

        :return: The timeout of this BackupSnapshotPolicyGET.  # noqa: E501
        :rtype: int
        """
        return self._timeout

    @timeout.setter
    def timeout(self, timeout):
        """Sets the timeout of this BackupSnapshotPolicyGET.

        Maximum number of seconds to wait on the snapshot creation  # noqa: E501

        :param timeout: The timeout of this BackupSnapshotPolicyGET.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and timeout is None:
            raise ValueError("Invalid value for `timeout`, must not be `None`")  # noqa: E501

        self._timeout = timeout

    @property
    def retry_pause(self):
        """Gets the retry_pause of this BackupSnapshotPolicyGET.  # noqa: E501

        Number of seconds to pause between retries if cooperative_failure_behaviour is set to RETRY_IF_SNAPSHOT_TIMEOUT  # noqa: E501

        :return: The retry_pause of this BackupSnapshotPolicyGET.  # noqa: E501
        :rtype: int
        """
        return self._retry_pause

    @retry_pause.setter
    def retry_pause(self, retry_pause):
        """Sets the retry_pause of this BackupSnapshotPolicyGET.

        Number of seconds to pause between retries if cooperative_failure_behaviour is set to RETRY_IF_SNAPSHOT_TIMEOUT  # noqa: E501

        :param retry_pause: The retry_pause of this BackupSnapshotPolicyGET.  # noqa: E501
        :type: int
        """

        self._retry_pause = retry_pause

    @property
    def retry_times(self):
        """Gets the retry_times of this BackupSnapshotPolicyGET.  # noqa: E501

        Number of times to retry the cooperative snapshot if cooperative_failure_behaviour is set to RETRY_IF_SNAPSHOT_TIMEOUT  # noqa: E501

        :return: The retry_times of this BackupSnapshotPolicyGET.  # noqa: E501
        :rtype: int
        """
        return self._retry_times

    @retry_times.setter
    def retry_times(self, retry_times):
        """Sets the retry_times of this BackupSnapshotPolicyGET.

        Number of times to retry the cooperative snapshot if cooperative_failure_behaviour is set to RETRY_IF_SNAPSHOT_TIMEOUT  # noqa: E501

        :param retry_times: The retry_times of this BackupSnapshotPolicyGET.  # noqa: E501
        :type: int
        """

        self._retry_times = retry_times

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(BackupSnapshotPolicyGET, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, BackupSnapshotPolicyGET):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, BackupSnapshotPolicyGET):
            return True

        return self.to_dict() != other.to_dict()
