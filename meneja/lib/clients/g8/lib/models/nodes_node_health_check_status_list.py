# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
    G8 API

    RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

    OpenAPI spec version: v4.0
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from meneja.lib.clients.g8.lib.configuration import Configuration


class NodesNodeHealthCheckStatusLIST(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        "grid_id": "int",
        "node_id": "int",
        "node_name": "str",
        "status": "str",
        "muted_count": "int",
        "faulty_categories": "list[str]",
    }

    attribute_map = {
        "grid_id": "grid_id",
        "node_id": "node_id",
        "node_name": "node_name",
        "status": "status",
        "muted_count": "muted_count",
        "faulty_categories": "faulty_categories",
    }

    def __init__(
        self,
        grid_id=None,
        node_id=None,
        node_name=None,
        status=None,
        muted_count=None,
        faulty_categories=None,
        _configuration=None,
    ):  # noqa: E501
        """NodesNodeHealthCheckStatusLIST - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._grid_id = None
        self._node_id = None
        self._node_name = None
        self._status = None
        self._muted_count = None
        self._faulty_categories = None
        self.discriminator = None

        self.grid_id = grid_id
        self.node_id = node_id
        self.node_name = node_name
        self.status = status
        self.muted_count = muted_count
        self.faulty_categories = faulty_categories

    @property
    def grid_id(self):
        """Gets the grid_id of this NodesNodeHealthCheckStatusLIST.  # noqa: E501

        Grid id for the node  # noqa: E501

        :return: The grid_id of this NodesNodeHealthCheckStatusLIST.  # noqa: E501
        :rtype: int
        """
        return self._grid_id

    @grid_id.setter
    def grid_id(self, grid_id):
        """Sets the grid_id of this NodesNodeHealthCheckStatusLIST.

        Grid id for the node  # noqa: E501

        :param grid_id: The grid_id of this NodesNodeHealthCheckStatusLIST.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and grid_id is None:
            raise ValueError("Invalid value for `grid_id`, must not be `None`")  # noqa: E501

        self._grid_id = grid_id

    @property
    def node_id(self):
        """Gets the node_id of this NodesNodeHealthCheckStatusLIST.  # noqa: E501

        Node id  # noqa: E501

        :return: The node_id of this NodesNodeHealthCheckStatusLIST.  # noqa: E501
        :rtype: int
        """
        return self._node_id

    @node_id.setter
    def node_id(self, node_id):
        """Sets the node_id of this NodesNodeHealthCheckStatusLIST.

        Node id  # noqa: E501

        :param node_id: The node_id of this NodesNodeHealthCheckStatusLIST.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and node_id is None:
            raise ValueError("Invalid value for `node_id`, must not be `None`")  # noqa: E501

        self._node_id = node_id

    @property
    def node_name(self):
        """Gets the node_name of this NodesNodeHealthCheckStatusLIST.  # noqa: E501

        Node name  # noqa: E501

        :return: The node_name of this NodesNodeHealthCheckStatusLIST.  # noqa: E501
        :rtype: str
        """
        return self._node_name

    @node_name.setter
    def node_name(self, node_name):
        """Sets the node_name of this NodesNodeHealthCheckStatusLIST.

        Node name  # noqa: E501

        :param node_name: The node_name of this NodesNodeHealthCheckStatusLIST.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and node_name is None:
            raise ValueError("Invalid value for `node_name`, must not be `None`")  # noqa: E501

        self._node_name = node_name

    @property
    def status(self):
        """Gets the status of this NodesNodeHealthCheckStatusLIST.  # noqa: E501

        Worst health check status at the node  # noqa: E501

        :return: The status of this NodesNodeHealthCheckStatusLIST.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this NodesNodeHealthCheckStatusLIST.

        Worst health check status at the node  # noqa: E501

        :param status: The status of this NodesNodeHealthCheckStatusLIST.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and status is None:
            raise ValueError("Invalid value for `status`, must not be `None`")  # noqa: E501
        allowed_values = ["OK", "WARNING", "ERROR", "EXPIRED", "SKIPPED"]  # noqa: E501
        if self._configuration.client_side_validation and status not in allowed_values:
            raise ValueError(
                "Invalid value for `status` ({0}), must be one of {1}".format(status, allowed_values)  # noqa: E501
            )

        self._status = status

    @property
    def muted_count(self):
        """Gets the muted_count of this NodesNodeHealthCheckStatusLIST.  # noqa: E501

        Number of muted health check at the node  # noqa: E501

        :return: The muted_count of this NodesNodeHealthCheckStatusLIST.  # noqa: E501
        :rtype: int
        """
        return self._muted_count

    @muted_count.setter
    def muted_count(self, muted_count):
        """Sets the muted_count of this NodesNodeHealthCheckStatusLIST.

        Number of muted health check at the node  # noqa: E501

        :param muted_count: The muted_count of this NodesNodeHealthCheckStatusLIST.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and muted_count is None:
            raise ValueError("Invalid value for `muted_count`, must not be `None`")  # noqa: E501

        self._muted_count = muted_count

    @property
    def faulty_categories(self):
        """Gets the faulty_categories of this NodesNodeHealthCheckStatusLIST.  # noqa: E501

        List of categories that has faulty status  # noqa: E501

        :return: The faulty_categories of this NodesNodeHealthCheckStatusLIST.  # noqa: E501
        :rtype: list[str]
        """
        return self._faulty_categories

    @faulty_categories.setter
    def faulty_categories(self, faulty_categories):
        """Sets the faulty_categories of this NodesNodeHealthCheckStatusLIST.

        List of categories that has faulty status  # noqa: E501

        :param faulty_categories: The faulty_categories of this NodesNodeHealthCheckStatusLIST.  # noqa: E501
        :type: list[str]
        """
        if self._configuration.client_side_validation and faulty_categories is None:
            raise ValueError("Invalid value for `faulty_categories`, must not be `None`")  # noqa: E501

        self._faulty_categories = faulty_categories

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(lambda x: x.to_dict() if hasattr(x, "to_dict") else x, value))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(
                    map(
                        lambda item: (item[0], item[1].to_dict()) if hasattr(item[1], "to_dict") else item,
                        value.items(),
                    )
                )
            else:
                result[attr] = value
        if issubclass(NodesNodeHealthCheckStatusLIST, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, NodesNodeHealthCheckStatusLIST):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, NodesNodeHealthCheckStatusLIST):
            return True

        return self.to_dict() != other.to_dict()
