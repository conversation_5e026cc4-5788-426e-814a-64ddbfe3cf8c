# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# flake8: noqa
# coding: utf-8

"""
    G8 API

    RESTful api to the G8 API is internal and should never be used directly by non GIG.tech software  # noqa: E501

    OpenAPI spec version: v4.0
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

import re  # noqa: F401

# python 2 and python 3 compatibility library
import six

from meneja.lib.clients.g8.lib.api_client import ApiClient


class BackupsApi(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    Ref: https://github.com/swagger-api/swagger-codegen
    """

    def __init__(self, api_client=None):
        if api_client is None:
            api_client = ApiClient()
        self.api_client = api_client

    def create_backup(self, payload, **kwargs):  # noqa: E501
        """Create new backup  # noqa: E501

        Create new backup  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_backup(payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param BackupBackupStructCREATE payload: (required)
        :param str x_fields: An optional fields mask
        :return: BackupBackupStructGET
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.create_backup_with_http_info(payload, **kwargs)  # noqa: E501
        else:
            (data) = self.create_backup_with_http_info(payload, **kwargs)  # noqa: E501
            return data

    def create_backup_with_http_info(self, payload, **kwargs):  # noqa: E501
        """Create new backup  # noqa: E501

        Create new backup  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_backup_with_http_info(payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param BackupBackupStructCREATE payload: (required)
        :param str x_fields: An optional fields mask
        :return: BackupBackupStructGET
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["payload", "x_fields"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method create_backup" % key)
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'payload' is set
        if self.api_client.client_side_validation and (
            "payload" not in params or params["payload"] is None
        ):  # noqa: E501
            raise ValueError("Missing the required parameter `payload` when calling `create_backup`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        if "payload" in params:
            body_params = params["payload"]
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/backups",
            "POST",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="BackupBackupStructGET",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def create_backup_policy(self, payload, **kwargs):  # noqa: E501
        """Create new backup policy  # noqa: E501

        Create new backup policy  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_backup_policy(payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param BackupBackupPolicyStructCREATE payload: (required)
        :param str x_fields: An optional fields mask
        :return: BackupBackupPolicyStructGET
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.create_backup_policy_with_http_info(payload, **kwargs)  # noqa: E501
        else:
            (data) = self.create_backup_policy_with_http_info(payload, **kwargs)  # noqa: E501
            return data

    def create_backup_policy_with_http_info(self, payload, **kwargs):  # noqa: E501
        """Create new backup policy  # noqa: E501

        Create new backup policy  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_backup_policy_with_http_info(payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param BackupBackupPolicyStructCREATE payload: (required)
        :param str x_fields: An optional fields mask
        :return: BackupBackupPolicyStructGET
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["payload", "x_fields"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method create_backup_policy" % key)
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'payload' is set
        if self.api_client.client_side_validation and (
            "payload" not in params or params["payload"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `payload` when calling `create_backup_policy`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        if "payload" in params:
            body_params = params["payload"]
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/backups/policies",
            "POST",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="BackupBackupPolicyStructGET",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def create_backup_target(self, payload, **kwargs):  # noqa: E501
        """Create new backup target  # noqa: E501

        Create new backup target  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_backup_target(payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param BackupBackupTargetStructCREATE payload: (required)
        :param str x_fields: An optional fields mask
        :return: BackupBackupTargetStructGET
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.create_backup_target_with_http_info(payload, **kwargs)  # noqa: E501
        else:
            (data) = self.create_backup_target_with_http_info(payload, **kwargs)  # noqa: E501
            return data

    def create_backup_target_with_http_info(self, payload, **kwargs):  # noqa: E501
        """Create new backup target  # noqa: E501

        Create new backup target  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_backup_target_with_http_info(payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param BackupBackupTargetStructCREATE payload: (required)
        :param str x_fields: An optional fields mask
        :return: BackupBackupTargetStructGET
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["payload", "x_fields"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method create_backup_target" % key)
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'payload' is set
        if self.api_client.client_side_validation and (
            "payload" not in params or params["payload"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `payload` when calling `create_backup_target`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        if "payload" in params:
            body_params = params["payload"]
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/backups/targets",
            "POST",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="BackupBackupTargetStructGET",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def delete_backup(self, backup_id, **kwargs):  # noqa: E501
        """Delete backup  # noqa: E501

        Delete backup  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_backup(backup_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str backup_id: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.delete_backup_with_http_info(backup_id, **kwargs)  # noqa: E501
        else:
            (data) = self.delete_backup_with_http_info(backup_id, **kwargs)  # noqa: E501
            return data

    def delete_backup_with_http_info(self, backup_id, **kwargs):  # noqa: E501
        """Delete backup  # noqa: E501

        Delete backup  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_backup_with_http_info(backup_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str backup_id: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["backup_id"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method delete_backup" % key)
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'backup_id' is set
        if self.api_client.client_side_validation and (
            "backup_id" not in params or params["backup_id"] is None
        ):  # noqa: E501
            raise ValueError("Missing the required parameter `backup_id` when calling `delete_backup`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "backup_id" in params:
            path_params["backup_id"] = params["backup_id"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/backups/{backup_id}",
            "DELETE",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def delete_backup_policy(self, policy_id, **kwargs):  # noqa: E501
        """Delete backup policy  # noqa: E501

        Delete backup policy  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_backup_policy(policy_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str policy_id: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.delete_backup_policy_with_http_info(policy_id, **kwargs)  # noqa: E501
        else:
            (data) = self.delete_backup_policy_with_http_info(policy_id, **kwargs)  # noqa: E501
            return data

    def delete_backup_policy_with_http_info(self, policy_id, **kwargs):  # noqa: E501
        """Delete backup policy  # noqa: E501

        Delete backup policy  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_backup_policy_with_http_info(policy_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str policy_id: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["policy_id"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method delete_backup_policy" % key)
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'policy_id' is set
        if self.api_client.client_side_validation and (
            "policy_id" not in params or params["policy_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `policy_id` when calling `delete_backup_policy`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "policy_id" in params:
            path_params["policy_id"] = params["policy_id"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/backups/policies/{policy_id}",
            "DELETE",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def delete_backup_target(self, target_id, **kwargs):  # noqa: E501
        """Delete backup target  # noqa: E501

        Delete backup target  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_backup_target(target_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int target_id: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.delete_backup_target_with_http_info(target_id, **kwargs)  # noqa: E501
        else:
            (data) = self.delete_backup_target_with_http_info(target_id, **kwargs)  # noqa: E501
            return data

    def delete_backup_target_with_http_info(self, target_id, **kwargs):  # noqa: E501
        """Delete backup target  # noqa: E501

        Delete backup target  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_backup_target_with_http_info(target_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int target_id: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["target_id"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method delete_backup_target" % key)
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'target_id' is set
        if self.api_client.client_side_validation and (
            "target_id" not in params or params["target_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `target_id` when calling `delete_backup_target`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "target_id" in params:
            path_params["target_id"] = params["target_id"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/backups/targets/{target_id}",
            "DELETE",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def delete_target_v_mbackups(self, target_id, vm_id, **kwargs):  # noqa: E501
        """delete vm backups  # noqa: E501

        Delete vm backups  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_target_v_mbackups(target_id, vm_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int target_id: (required)
        :param int vm_id: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.delete_target_v_mbackups_with_http_info(target_id, vm_id, **kwargs)  # noqa: E501
        else:
            (data) = self.delete_target_v_mbackups_with_http_info(target_id, vm_id, **kwargs)  # noqa: E501
            return data

    def delete_target_v_mbackups_with_http_info(self, target_id, vm_id, **kwargs):  # noqa: E501
        """delete vm backups  # noqa: E501

        Delete vm backups  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_target_v_mbackups_with_http_info(target_id, vm_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int target_id: (required)
        :param int vm_id: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["target_id", "vm_id"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method delete_target_v_mbackups" % key)
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'target_id' is set
        if self.api_client.client_side_validation and (
            "target_id" not in params or params["target_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `target_id` when calling `delete_target_v_mbackups`"
            )  # noqa: E501
        # verify the required parameter 'vm_id' is set
        if self.api_client.client_side_validation and ("vm_id" not in params or params["vm_id"] is None):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `vm_id` when calling `delete_target_v_mbackups`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "target_id" in params:
            path_params["target_id"] = params["target_id"]  # noqa: E501
        if "vm_id" in params:
            path_params["vm_id"] = params["vm_id"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/backups/targets/{target_id}/vms/{vm_id}",
            "DELETE",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def get_backup(self, backup_id, **kwargs):  # noqa: E501
        """Get backup info  # noqa: E501

        Get backup info  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_backup(backup_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str backup_id: (required)
        :param str x_fields: An optional fields mask
        :return: BackupBackupStructGET
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.get_backup_with_http_info(backup_id, **kwargs)  # noqa: E501
        else:
            (data) = self.get_backup_with_http_info(backup_id, **kwargs)  # noqa: E501
            return data

    def get_backup_with_http_info(self, backup_id, **kwargs):  # noqa: E501
        """Get backup info  # noqa: E501

        Get backup info  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_backup_with_http_info(backup_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str backup_id: (required)
        :param str x_fields: An optional fields mask
        :return: BackupBackupStructGET
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["backup_id", "x_fields"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method get_backup" % key)
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'backup_id' is set
        if self.api_client.client_side_validation and (
            "backup_id" not in params or params["backup_id"] is None
        ):  # noqa: E501
            raise ValueError("Missing the required parameter `backup_id` when calling `get_backup`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "backup_id" in params:
            path_params["backup_id"] = params["backup_id"]  # noqa: E501

        query_params = []

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/backups/{backup_id}",
            "GET",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="BackupBackupStructGET",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def get_backup_policy(self, policy_id, **kwargs):  # noqa: E501
        """Get backup policy info  # noqa: E501

        Get backup policy info  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_backup_policy(policy_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str policy_id: (required)
        :param str x_fields: An optional fields mask
        :return: BackupBackupPolicyStructGET
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.get_backup_policy_with_http_info(policy_id, **kwargs)  # noqa: E501
        else:
            (data) = self.get_backup_policy_with_http_info(policy_id, **kwargs)  # noqa: E501
            return data

    def get_backup_policy_with_http_info(self, policy_id, **kwargs):  # noqa: E501
        """Get backup policy info  # noqa: E501

        Get backup policy info  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_backup_policy_with_http_info(policy_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str policy_id: (required)
        :param str x_fields: An optional fields mask
        :return: BackupBackupPolicyStructGET
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["policy_id", "x_fields"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method get_backup_policy" % key)
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'policy_id' is set
        if self.api_client.client_side_validation and (
            "policy_id" not in params or params["policy_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `policy_id` when calling `get_backup_policy`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "policy_id" in params:
            path_params["policy_id"] = params["policy_id"]  # noqa: E501

        query_params = []

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/backups/policies/{policy_id}",
            "GET",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="BackupBackupPolicyStructGET",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def get_backup_target(self, target_id, **kwargs):  # noqa: E501
        """Get backup target info  # noqa: E501

        Get backup target info  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_backup_target(target_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int target_id: (required)
        :param str x_fields: An optional fields mask
        :return: BackupBackupTargetStructGET
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.get_backup_target_with_http_info(target_id, **kwargs)  # noqa: E501
        else:
            (data) = self.get_backup_target_with_http_info(target_id, **kwargs)  # noqa: E501
            return data

    def get_backup_target_with_http_info(self, target_id, **kwargs):  # noqa: E501
        """Get backup target info  # noqa: E501

        Get backup target info  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_backup_target_with_http_info(target_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int target_id: (required)
        :param str x_fields: An optional fields mask
        :return: BackupBackupTargetStructGET
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["target_id", "x_fields"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method get_backup_target" % key)
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'target_id' is set
        if self.api_client.client_side_validation and (
            "target_id" not in params or params["target_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `target_id` when calling `get_backup_target`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "target_id" in params:
            path_params["target_id"] = params["target_id"]  # noqa: E501

        query_params = []

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/backups/targets/{target_id}",
            "GET",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="BackupBackupTargetStructGET",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def list_backup_policy(self, **kwargs):  # noqa: E501
        """List all backup policies  # noqa: E501

        List backup policy  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_backup_policy(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int limit: Flag to limit the amount of results.
        :param int start_after: Start returning records after index
        :param str sort_by: sorting field
        :param int sort_direction: sorting direction. 1 for asc and -1 for desc
        :param str x_fields: An optional fields mask
        :return: BackupPolicyStructs
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.list_backup_policy_with_http_info(**kwargs)  # noqa: E501
        else:
            (data) = self.list_backup_policy_with_http_info(**kwargs)  # noqa: E501
            return data

    def list_backup_policy_with_http_info(self, **kwargs):  # noqa: E501
        """List all backup policies  # noqa: E501

        List backup policy  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_backup_policy_with_http_info(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int limit: Flag to limit the amount of results.
        :param int start_after: Start returning records after index
        :param str sort_by: sorting field
        :param int sort_direction: sorting direction. 1 for asc and -1 for desc
        :param str x_fields: An optional fields mask
        :return: BackupPolicyStructs
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["limit", "start_after", "sort_by", "sort_direction", "x_fields"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method list_backup_policy" % key)
            params[key] = val
        del params["kwargs"]

        collection_formats = {}

        path_params = {}

        query_params = []
        if "limit" in params:
            query_params.append(("limit", params["limit"]))  # noqa: E501
        if "start_after" in params:
            query_params.append(("start_after", params["start_after"]))  # noqa: E501
        if "sort_by" in params:
            query_params.append(("sort_by", params["sort_by"]))  # noqa: E501
        if "sort_direction" in params:
            query_params.append(("sort_direction", params["sort_direction"]))  # noqa: E501

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/backups/policies",
            "GET",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="BackupPolicyStructs",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def list_backup_target(self, **kwargs):  # noqa: E501
        """List all BackupTargets  # noqa: E501

        List backup target  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_backup_target(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int limit: Flag to limit the amount of results.
        :param int start_after: Start returning records after index
        :param str sort_by: sorting field
        :param int sort_direction: sorting direction. 1 for asc and -1 for desc
        :param str x_fields: An optional fields mask
        :return: BackupTargetStructs
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.list_backup_target_with_http_info(**kwargs)  # noqa: E501
        else:
            (data) = self.list_backup_target_with_http_info(**kwargs)  # noqa: E501
            return data

    def list_backup_target_with_http_info(self, **kwargs):  # noqa: E501
        """List all BackupTargets  # noqa: E501

        List backup target  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_backup_target_with_http_info(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int limit: Flag to limit the amount of results.
        :param int start_after: Start returning records after index
        :param str sort_by: sorting field
        :param int sort_direction: sorting direction. 1 for asc and -1 for desc
        :param str x_fields: An optional fields mask
        :return: BackupTargetStructs
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["limit", "start_after", "sort_by", "sort_direction", "x_fields"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method list_backup_target" % key)
            params[key] = val
        del params["kwargs"]

        collection_formats = {}

        path_params = {}

        query_params = []
        if "limit" in params:
            query_params.append(("limit", params["limit"]))  # noqa: E501
        if "start_after" in params:
            query_params.append(("start_after", params["start_after"]))  # noqa: E501
        if "sort_by" in params:
            query_params.append(("sort_by", params["sort_by"]))  # noqa: E501
        if "sort_direction" in params:
            query_params.append(("sort_direction", params["sort_direction"]))  # noqa: E501

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/backups/targets",
            "GET",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="BackupTargetStructs",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def list_backups(self, **kwargs):  # noqa: E501
        """List all backups  # noqa: E501

        List backups  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_backups(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int limit: Flag to limit the amount of results.
        :param int start_after: Start returning records after index
        :param str sort_by: sorting field
        :param int sort_direction: sorting direction. 1 for asc and -1 for desc
        :param str location: Location name
        :param int vm_id: Only retrun snapshots created for specific virtual machine
        :param int account_id: Account id
        :param int cloudspace_id: Cloudspace id
        :param int policy: Policy id
        :param int target_id: Backup target id
        :param list[str] status: Backup status
        :param bool exclude_expired: Exclude expired backups
        :param str x_fields: An optional fields mask
        :return: BackupStructs
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.list_backups_with_http_info(**kwargs)  # noqa: E501
        else:
            (data) = self.list_backups_with_http_info(**kwargs)  # noqa: E501
            return data

    def list_backups_with_http_info(self, **kwargs):  # noqa: E501
        """List all backups  # noqa: E501

        List backups  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_backups_with_http_info(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int limit: Flag to limit the amount of results.
        :param int start_after: Start returning records after index
        :param str sort_by: sorting field
        :param int sort_direction: sorting direction. 1 for asc and -1 for desc
        :param str location: Location name
        :param int vm_id: Only retrun snapshots created for specific virtual machine
        :param int account_id: Account id
        :param int cloudspace_id: Cloudspace id
        :param int policy: Policy id
        :param int target_id: Backup target id
        :param list[str] status: Backup status
        :param bool exclude_expired: Exclude expired backups
        :param str x_fields: An optional fields mask
        :return: BackupStructs
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = [
            "limit",
            "start_after",
            "sort_by",
            "sort_direction",
            "location",
            "vm_id",
            "account_id",
            "cloudspace_id",
            "policy",
            "target_id",
            "status",
            "exclude_expired",
            "x_fields",
        ]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method list_backups" % key)
            params[key] = val
        del params["kwargs"]

        collection_formats = {}

        path_params = {}

        query_params = []
        if "limit" in params:
            query_params.append(("limit", params["limit"]))  # noqa: E501
        if "start_after" in params:
            query_params.append(("start_after", params["start_after"]))  # noqa: E501
        if "sort_by" in params:
            query_params.append(("sort_by", params["sort_by"]))  # noqa: E501
        if "sort_direction" in params:
            query_params.append(("sort_direction", params["sort_direction"]))  # noqa: E501
        if "location" in params:
            query_params.append(("location", params["location"]))  # noqa: E501
        if "vm_id" in params:
            query_params.append(("vm_id", params["vm_id"]))  # noqa: E501
        if "account_id" in params:
            query_params.append(("account_id", params["account_id"]))  # noqa: E501
        if "cloudspace_id" in params:
            query_params.append(("cloudspace_id", params["cloudspace_id"]))  # noqa: E501
        if "policy" in params:
            query_params.append(("policy", params["policy"]))  # noqa: E501
        if "target_id" in params:
            query_params.append(("target_id", params["target_id"]))  # noqa: E501
        if "status" in params:
            query_params.append(("status", params["status"]))  # noqa: E501
            collection_formats["status"] = "csv"  # noqa: E501
        if "exclude_expired" in params:
            query_params.append(("exclude_expired", params["exclude_expired"]))  # noqa: E501

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/backups",
            "GET",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="BackupStructs",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def list_target_vm_backups(self, target_id, vm_id, **kwargs):  # noqa: E501
        """list vm backups  # noqa: E501

        list vm backups  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_target_vm_backups(target_id, vm_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int target_id: (required)
        :param int vm_id: (required)
        :param str x_fields: An optional fields mask
        :return: BackupStructs
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.list_target_vm_backups_with_http_info(target_id, vm_id, **kwargs)  # noqa: E501
        else:
            (data) = self.list_target_vm_backups_with_http_info(target_id, vm_id, **kwargs)  # noqa: E501
            return data

    def list_target_vm_backups_with_http_info(self, target_id, vm_id, **kwargs):  # noqa: E501
        """list vm backups  # noqa: E501

        list vm backups  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_target_vm_backups_with_http_info(target_id, vm_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int target_id: (required)
        :param int vm_id: (required)
        :param str x_fields: An optional fields mask
        :return: BackupStructs
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["target_id", "vm_id", "x_fields"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method list_target_vm_backups" % key)
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'target_id' is set
        if self.api_client.client_side_validation and (
            "target_id" not in params or params["target_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `target_id` when calling `list_target_vm_backups`"
            )  # noqa: E501
        # verify the required parameter 'vm_id' is set
        if self.api_client.client_side_validation and ("vm_id" not in params or params["vm_id"] is None):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `vm_id` when calling `list_target_vm_backups`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "target_id" in params:
            path_params["target_id"] = params["target_id"]  # noqa: E501
        if "vm_id" in params:
            path_params["vm_id"] = params["vm_id"]  # noqa: E501

        query_params = []

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/backups/targets/{target_id}/vms/{vm_id}/backups",
            "GET",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="BackupStructs",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def list_target_vms(self, target_id, **kwargs):  # noqa: E501
        """list backup target vms  # noqa: E501

        List target vms  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_target_vms(target_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int target_id: (required)
        :param str x_fields: An optional fields mask
        :return: BackupVMStructs
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.list_target_vms_with_http_info(target_id, **kwargs)  # noqa: E501
        else:
            (data) = self.list_target_vms_with_http_info(target_id, **kwargs)  # noqa: E501
            return data

    def list_target_vms_with_http_info(self, target_id, **kwargs):  # noqa: E501
        """list backup target vms  # noqa: E501

        List target vms  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_target_vms_with_http_info(target_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int target_id: (required)
        :param str x_fields: An optional fields mask
        :return: BackupVMStructs
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["target_id", "x_fields"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method list_target_vms" % key)
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'target_id' is set
        if self.api_client.client_side_validation and (
            "target_id" not in params or params["target_id"] is None
        ):  # noqa: E501
            raise ValueError("Missing the required parameter `target_id` when calling `list_target_vms`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "target_id" in params:
            path_params["target_id"] = params["target_id"]  # noqa: E501

        query_params = []

        header_params = {}
        if "x_fields" in params:
            header_params["X-Fields"] = params["x_fields"]  # noqa: E501

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/backups/targets/{target_id}/vms",
            "GET",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type="BackupVMStructs",  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def sync_target_backups(self, target_id, **kwargs):  # noqa: E501
        """Sync target backups  # noqa: E501

        Sync target backups  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.sync_target_backups(target_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int target_id: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.sync_target_backups_with_http_info(target_id, **kwargs)  # noqa: E501
        else:
            (data) = self.sync_target_backups_with_http_info(target_id, **kwargs)  # noqa: E501
            return data

    def sync_target_backups_with_http_info(self, target_id, **kwargs):  # noqa: E501
        """Sync target backups  # noqa: E501

        Sync target backups  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.sync_target_backups_with_http_info(target_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int target_id: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["target_id"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method sync_target_backups" % key)
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'target_id' is set
        if self.api_client.client_side_validation and (
            "target_id" not in params or params["target_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `target_id` when calling `sync_target_backups`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "target_id" in params:
            path_params["target_id"] = params["target_id"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/backups/targets/{target_id}/sync",
            "POST",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def update_backup_policy(self, policy_id, payload, **kwargs):  # noqa: E501
        """Update backup policy info  # noqa: E501

        Update backup policy info  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.update_backup_policy(policy_id, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str policy_id: (required)
        :param BackupBackupPolicyStructUPDATE payload: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.update_backup_policy_with_http_info(policy_id, payload, **kwargs)  # noqa: E501
        else:
            (data) = self.update_backup_policy_with_http_info(policy_id, payload, **kwargs)  # noqa: E501
            return data

    def update_backup_policy_with_http_info(self, policy_id, payload, **kwargs):  # noqa: E501
        """Update backup policy info  # noqa: E501

        Update backup policy info  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.update_backup_policy_with_http_info(policy_id, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str policy_id: (required)
        :param BackupBackupPolicyStructUPDATE payload: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["policy_id", "payload"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method update_backup_policy" % key)
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'policy_id' is set
        if self.api_client.client_side_validation and (
            "policy_id" not in params or params["policy_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `policy_id` when calling `update_backup_policy`"
            )  # noqa: E501
        # verify the required parameter 'payload' is set
        if self.api_client.client_side_validation and (
            "payload" not in params or params["payload"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `payload` when calling `update_backup_policy`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "policy_id" in params:
            path_params["policy_id"] = params["policy_id"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if "payload" in params:
            body_params = params["payload"]
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/backups/policies/{policy_id}",
            "PUT",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )

    def update_backup_target(self, target_id, payload, **kwargs):  # noqa: E501
        """Update backup target info  # noqa: E501

        Update backup target info  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.update_backup_target(target_id, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int target_id: (required)
        :param BackupBackupTargetStructUPDATE payload: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs["_return_http_data_only"] = True
        if kwargs.get("async_req"):
            return self.update_backup_target_with_http_info(target_id, payload, **kwargs)  # noqa: E501
        else:
            (data) = self.update_backup_target_with_http_info(target_id, payload, **kwargs)  # noqa: E501
            return data

    def update_backup_target_with_http_info(self, target_id, payload, **kwargs):  # noqa: E501
        """Update backup target info  # noqa: E501

        Update backup target info  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.update_backup_target_with_http_info(target_id, payload, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int target_id: (required)
        :param BackupBackupTargetStructUPDATE payload: (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ["target_id", "payload"]  # noqa: E501
        all_params.append("async_req")
        all_params.append("_return_http_data_only")
        all_params.append("_preload_content")
        all_params.append("_request_timeout")

        params = locals()
        for key, val in six.iteritems(params["kwargs"]):
            if key not in all_params:
                raise TypeError("Got an unexpected keyword argument '%s'" " to method update_backup_target" % key)
            params[key] = val
        del params["kwargs"]
        # verify the required parameter 'target_id' is set
        if self.api_client.client_side_validation and (
            "target_id" not in params or params["target_id"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `target_id` when calling `update_backup_target`"
            )  # noqa: E501
        # verify the required parameter 'payload' is set
        if self.api_client.client_side_validation and (
            "payload" not in params or params["payload"] is None
        ):  # noqa: E501
            raise ValueError(
                "Missing the required parameter `payload` when calling `update_backup_target`"
            )  # noqa: E501

        collection_formats = {}

        path_params = {}
        if "target_id" in params:
            path_params["target_id"] = params["target_id"]  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if "payload" in params:
            body_params = params["payload"]
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(["application/json"])  # noqa: E501

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(  # noqa: E501
            ["application/json"]
        )  # noqa: E501

        # Authentication setting
        auth_settings = ["Bearer"]  # noqa: E501

        return self.api_client.call_api(
            "/backups/targets/{target_id}",
            "PUT",
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get("async_req"),
            _return_http_data_only=params.get("_return_http_data_only"),
            _preload_content=params.get("_preload_content", True),
            _request_timeout=params.get("_request_timeout"),
            collection_formats=collection_formats,
        )
