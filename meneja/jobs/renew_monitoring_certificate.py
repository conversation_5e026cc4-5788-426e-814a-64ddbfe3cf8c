# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, REPRODUC<PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
import base64
import logging
from os import environ
from dynaqueue.scheduler import schedule
from kubernetes import client, config  # pylint: disable=E0611
from meneja.model.vco.certificate import CustomerSSLCertificate

logger = logging.getLogger()
logging.basicConfig(level=logging.INFO)

NAMESPACE = "monitoring"
TLS_SECRETS = ["grafana-tls", "prometheus-tls"]
MONITORING_DOMAINS = environ.get(
    "MONITORING_URL", "grafana.meneja.gig-tech.cloudbuilders.be,prometheus.meneja.gig-tech.cloudbuilders.be"
).split(",")
CUSTOMER_ID = environ.get("CUSTOMER_ID", "gigtech_nv_--_meneja_1")


def _b64(s: str) -> str:
    return base64.b64encode(s.encode()).decode()


@schedule(cron="0 6 * * *", description="Check monitoring certificate validity")
def sync_certs_to_kubernetes():
    """Check and update monitoring certificates"""
    config.load_incluster_config()
    v1 = client.CoreV1Api()

    for i in range(2):
        secret_name = TLS_SECRETS[i]
        domain = MONITORING_DOMAINS[i]

        logger.info("Syncing TLS for domain: %s -> secret: %s", domain, secret_name)

        try:
            db_cert: CustomerSSLCertificate = CustomerSSLCertificate.get_by_domain(
                customer_id=CUSTOMER_ID, domain=domain
            )

            if not db_cert or not db_cert.crt or not db_cert.key:
                logger.warning("[!] Missing cert/key in DB for %s", domain)
                continue

            secret = v1.read_namespaced_secret(secret_name, NAMESPACE)
            secret_cert = base64.b64decode(secret.data.get("tls.crt", "")).decode("utf-8")
            secret_key = base64.b64decode(secret.data.get("tls.key", "")).decode("utf-8")

            if db_cert.crt.strip() != secret_cert.strip() or db_cert.key.strip() != secret_key.strip():
                logger.info("Cert or key mismatch for %s, updating Kubernetes secret", domain)

                patch_body = {"data": {"tls.crt": _b64(db_cert.crt), "tls.key": _b64(db_cert.key)}}

                v1.patch_namespaced_secret(secret_name, NAMESPACE, patch_body)
                logger.info("Updated %s in cluster", secret_name)

            else:
                logger.info("No changes needed for %s", secret_name)

        except Exception:  # pylint: disable=broad-exception-caught
            logger.exception("Failed to sync TLS for %s", secret_name)
