# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, REPRODUC<PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

from typing import List, Sequence

from mongoengine import Document, ListField, StringField


class PreReleaseFeatures(Document):
    """Represents pre-release features in the system."""

    feature = StringField(help_text="Pre release feature", unique=True, required=True)
    vcos = ListField(help_text="List of VCOs that can see the pre-release feature")

    meta = {
        "indexes": ["feature"],
        "strict": False,
    }

    @classmethod
    def list(cls) -> Sequence["PreReleaseFeatures"]:
        """List all pre-release features"""
        return cls.objects()

    @classmethod
    def get_by_vco(cls, vco: str = None) -> List[str]:
        """Return list of feature names available to a VCO"""
        if not vco:
            return [doc.feature for doc in cls.objects()]
        return [doc.feature for doc in cls.objects(vcos__contains=vco)]

    @classmethod
    def delete_by_feature(cls, feature: str) -> bool:
        """Delete pre-release feature by feature name"""
        result = cls.objects(feature=feature).delete()
        return result > 0

    @classmethod
    def add_vco_to_feature(cls, feature: str, vco: str) -> bool:
        """Add a VCO to the feature. Raises if feature does not exist."""
        doc = cls.objects(feature=feature).first()
        if not doc:
            raise ValueError(f"Feature '{feature}' does not exist")

        if vco not in doc.vcos:
            doc.vcos.append(vco)
            doc.save()
            return True
        return False

    @classmethod
    def remove_vco_from_feature(cls, feature: str, vco: str) -> bool:
        """Remove a VCO from the feature. Deletes the doc if no VCOs remain."""
        doc = cls.objects(feature=feature).first()
        if not doc or vco not in doc.vcos:
            return False
        doc.vcos.remove(vco)
        if not doc.vcos:
            doc.delete()
        else:
            doc.save()
        return True
