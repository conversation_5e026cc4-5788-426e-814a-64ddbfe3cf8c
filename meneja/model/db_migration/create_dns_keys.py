# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

import glob
import os
import subprocess
from time import time

from pymongo import MongoClient
from pymongo.database import Database

from meneja.lib.connection import MongoConnection


def install():
    """Create DNS signing keys"""
    client: MongoClient = MongoConnection.get_client()
    database: Database = client.get_database()
    zone_name = ""
    key_dir = "/tmp"
    zone_files = database.get_collection("dns_zone")
    zone_keys = database.get_collection("dns_signing_key")
    for zone in zone_files.find():
        zone_name = zone["_id"][0:-5]
        subprocess.run(
            [
                "dnssec-keygen",
                "-a",
                "RSASHA256",
                "-b",
                "2048",
                "-n",
                "ZONE",
                "-K",
                key_dir,
                zone_name,
            ],
            check=True,
        )
        subprocess.run(
            [
                "dnssec-keygen",
                "-a",
                "RSASHA256",
                "-b",
                "2048",
                "-n",
                "ZONE",
                "-f",
                "KSK",
                "-K",
                key_dir,
                zone_name,
            ],
            check=True,
        )
        now = time()
        key_files = glob.glob(f"{key_dir}/K{zone_name}.+*.key")
        for key_file in key_files:
            with open(
                key_file,
                "rb",
            ) as kf:
                content = kf.read()
                zone_keys.insert_one(
                    dict(zone_name=zone_name, file_name=kf.name.split("/")[2], content=content, created_at=int(now))
                )
                os.remove(key_file)
        key_files = glob.glob(f"{key_dir}/K{zone_name}.+*.private")
        for key_file in key_files:
            with open(key_file, "rb") as kf:
                content = kf.read()
                zone_keys.insert_one(
                    dict(zone_name=zone_name, file_name=kf.name.split("/")[2], content=content, created_at=int(now))
                )
                os.remove(key_file)
