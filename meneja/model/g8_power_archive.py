# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, REPRODUC<PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

# pylint: disable=no-member, missing-function-docstring, missing-class-docstring

from mongoengine import Document, EmbeddedDocument, EmbeddedD<PERSON>ument<PERSON><PERSON><PERSON><PERSON>, In<PERSON><PERSON>ield, StringField

from meneja.lib.enumeration import NodePowerStatus


class NodeStatus(EmbeddedDocument):
    """Nodes power status"""

    meta = {"abstract": True}

    id = IntField(required=True, help_text="Node id")
    name = StringField(max_length=50, required=True, help_text="Name of the node")
    timestamp = IntField(required=True, help_text="Timestamp when the data was collected")
    status = StringField(choices=NodePowerStatus.values(), help_text="Node status")
    ping_status = StringField(choices=NodePowerStatus.values(), help_text="Node ping status")
    count = IntField(help_text="Count of status occurrences")


class SwitchStatus(EmbeddedDocument):
    """Switches power status"""

    meta = {"abstract": True}

    name = StringField(max_length=50, required=True, help_text="Name of the switch")
    timestamp = IntField(required=True, help_text="Timestamp when the data was collected")
    ping_status = StringField(choices=NodePowerStatus.values(), help_text="switch ping status")
    count = IntField(help_text="Count of status occurrences")


class G8PowerStatus(Document):
    """G8 nodes and switches power status history"""

    g8_name = StringField(max_length=50, required=True, help_text="Name of the G8")
    nodes = EmbeddedDocumentListField(NodeStatus, help_text="G8 nodes power status")
    switches = EmbeddedDocumentListField(SwitchStatus, help_text="G8 switches power status")

    @classmethod
    def get(cls, g8_name: str):
        """Get g8 power status history

        Args:
            name (str): g8_name

        Returns:
        G8NodeStatus: g8 node status history

        """
        return cls.objects.get(g8_name=g8_name)

    @classmethod
    def aggregate(cls, g8_name: str = None, start: int = None, end: int = None):
        """Aggregate g8 on/off statuses per status per G8 node/switch within  time range

        Args:
            g8_name str: G8 names.
            start (int, optional): starting timestamp. Defaults to None.
            end (int, optional): ending timestamp. Defaults to None.

        Returns:
            [generator[dict]]: aggregation of g8 nodes statuses
        """

        match = dict()
        match["g8_name"] = g8_name

        if start:
            match["nodes.timestamp"] = {"$gte": start}

        if end:
            match.setdefault("nodes.timestamp", {}).update({"$lt": end})

        nodes_group = dict()
        switches_group = dict()

        nodes_group["total_count"] = {"$sum": 1}
        nodes_group["_id"] = "$nodes.id"
        nodes_group["name"] = {"$first": "$nodes.name"}

        switches_group["total_count"] = {"$sum": 1}
        switches_group["_id"] = "$switches.name"

        project_status = dict()
        project_ping_status = dict()

        for status in NodePowerStatus.values():
            nodes_group[f"status_{status}"] = {"$sum": {"$cond": [{"$eq": ["$nodes.status", status]}, 1, 0]}}
            nodes_group[f"ping_{status}"] = {"$sum": {"$cond": [{"$eq": ["$nodes.ping_status", status]}, 1, 0]}}
            switches_group[f"ping_{status}"] = {"$sum": {"$cond": [{"$eq": ["$switches.ping_status", status]}, 1, 0]}}

            project_ping_status[status] = {"$multiply": [{"$divide": [f"$ping_{status}", "$total_count"]}, 100]}
            project_status[status] = {"$multiply": [{"$divide": [f"$status_{status}", "$total_count"]}, 100]}

        pipeline = [
            {
                "$facet": {
                    "nodes": [
                        {"$unwind": "$nodes"},
                        {"$match": match},
                        {"$group": nodes_group},
                        {"$project": {"name": 1, "status": project_status, "ping_status": project_ping_status}},
                    ],
                    "switches": [
                        {"$unwind": "$switches"},
                        {"$match": match},
                        {"$group": switches_group},
                        {"$project": {"ping_status": project_ping_status}},
                    ],
                },
            }
        ]

        return next(cls.objects.aggregate(*pipeline))
