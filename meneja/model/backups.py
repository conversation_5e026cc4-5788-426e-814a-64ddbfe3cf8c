# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, REPRODUC<PERSON>, D<PERSON>CLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

import time
from typing import Optional, Sequence

from mongoengine import (
    <PERSON><PERSON><PERSON><PERSON>ield,
    Document,
    EmbeddedDocument,
    Embedded<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    Embed<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
)
from mongoengine.errors import Does<PERSON>otExist

from meneja.lib.enumeration import PolicyTargetS3LockingMode, SnapshotPolicyFailureBehaviour


class SubscribedLocation(EmbeddedDocument):
    """Class for subscribed g8s locations."""

    location = StringField(help_text="The location of the subscribed g8s.")
    id = IntField(help_text="The target/policy ID associated with the location.")
    name = StringField(help_text="Name on G8.")
    cloudspace_name = StringField(help_text="The location of the subscribed g8s.")
    cloudspace_id = StringField(help_text="The ID of the cloudspace associated with the backup target.")
    overridable = BooleanField(default=True, help_text="Indicates if the location is overridable.")


class S3(EmbeddedDocument):
    """Class for S3 storage information."""

    url = StringField(required=True, help_text="The URL of the S3 storage.")
    region = StringField(required=True, help_text="The region of the S3 storage, e.g., 'us-east-1'.")
    access_key = StringField(required=True, help_text="The access key for the S3 storage.")
    secret_key = StringField(required=True, help_text="The secret key for the S3 storage.")
    bucket = StringField(required=True, help_text="The bucket name in the S3 storage.")
    locking_mode = StringField(
        choices=PolicyTargetS3LockingMode.values(), required=True, help_text="The locking mode for the S3 storage."
    )


class NamedEntity(EmbeddedDocument):
    """Class representing a named entity with an ID and name."""

    name = StringField(required=True, help_text="The name of the entity.")
    id = StringField(required=True, help_text="The unique ID of the entity.")


class Metadata(EmbeddedDocument):
    """Class for target metadata."""

    by_ce = BooleanField(help_text="Indicates if the target is managed by CE.")
    by_vco_admin = BooleanField(help_text="Indicates if the target is managed by VCO.")
    by_customer = BooleanField(help_text="Indicates if the target is managed by the customer.")
    ce = EmbeddedDocumentField(NamedEntity, help_text="The CE associated with the target, containing 'name' and 'id'.")
    vco = EmbeddedDocumentField(
        NamedEntity, help_text="The VCO associated with the target, containing 'name' and 'id'."
    )
    customer = EmbeddedDocumentField(
        NamedEntity, help_text="The customer associated with the target, containing 'name' and 'id'."
    )


class AssignedPolicies(EmbeddedDocument):
    """Class representing assigned policies to target"""

    policy_id = StringField(help_text="The password for the Restic repository.")
    subscribed_g8s = EmbeddedDocumentListField(SubscribedLocation, help_text="List of subscribed g8s locations.")


class Targets(Document):
    """Class representing a backup target."""

    name = StringField(
        required=True, readOnly=True, export=True, editable=False, help_text="The name of the backup target."
    )
    s3 = EmbeddedDocumentField(S3, help_text="The S3 storage information for the backup target.")
    restic_password = StringField(help_text="The password for the Restic repository.")
    created_at = IntField(
        readOnly=True,
        editable=False,
        export=True,
        default=time.time,
        help_text="The timestamp when the backup target was created.",
    )
    updated_at = IntField(help_text="The timestamp when the backup target was last updated.")
    subscribed_g8s = EmbeddedDocumentListField(SubscribedLocation, help_text="List of subscribed g8s locations.")
    metadata = EmbeddedDocumentField(Metadata, help_text="Metadata associated with the backup target.")
    assigned_policies = EmbeddedDocumentListField(
        AssignedPolicies, help_text="Metadata associated with the backup target."
    )

    @classmethod
    def get_by_id(
        cls,
        _id: str,
        customer_id: Optional[str] = None,
    ) -> Optional["Policies"]:
        """Retrieve a backup target by its ID, optionally filtered by customer ID in metadata"""
        try:
            target = cls.objects.get(id=_id)
            if customer_id is not None:
                if not target.metadata.customer.id == customer_id:
                    raise DoesNotExist("Target not found or does not belong to the specified customer.")
            # if ce_id is not None:
            #     if not (target.metadata.ce.id == ce_id and target.metadata.by_ce is True):
            #         raise DoesNotExist("Target not found or does not belong to the specified cloud enabler.")
            return target
        except cls.DoesNotExist:
            return DoesNotExist(f"Target with id {_id} is not found")

    @classmethod
    def list(
        cls,
        location: Optional[str] = None,
        by_customer: Optional[bool] = None,
        customer_id: Optional[str] = None,
        by_vco_admin: Optional[bool] = None,
        vco_id: Optional[str] = None,
        by_ce: Optional[str] = None,
        ce_id: Optional[str] = None,
    ) -> Sequence["Targets"]:
        """List all backup targets, optionally filtered by cloudspace ID, customer, or VCO admin.

        Args:
            cloudspace_id (Optional[str]): The ID of the cloudspace to filter by.
            by_customer (Optional[bool]): Whether the target is managed by the customer.
            customer_id (Optional[str]): The ID of the customer to filter by.
            by_vco_admin (Optional[bool]): Whether the target is managed by the VCO admin.
            vco_id (Optional[str]): The ID of the VCO to filter by.

        Returns:
            Sequence[Targets]: A list of backup target documents.
        """
        query = cls.objects()

        if location:
            query = query.filter(subscribed_g8s__location=location)

        if by_customer is True:
            query = query.filter(metadata__by_customer=by_customer)

        if customer_id:
            query = query.filter(metadata__customer__id=customer_id)

        if by_vco_admin is True:
            query = query.filter(metadata__by_vco_admin=by_vco_admin)

        if vco_id:
            query = query.filter(metadata__vco__id=vco_id)

        if by_ce is True:
            query = query.filter(metadata__by_ce=by_ce)

        if ce_id:
            query = query.filter(metadata__ce__id=ce_id)

        return query

    @classmethod
    def delete_by_id(
        cls,
        _id: str,
        customer_id: Optional[str] = None,
    ) -> bool:
        """Delete a backup target by its ID.

        Args:
            id (str): The ID of the backup target to delete.

        Returns:
            bool: True if the deletion was successful, False otherwise.
        """
        target = cls.objects.get(id=_id)
        if customer_id is not None:
            if not target.metadata.customer.id == customer_id:
                raise DoesNotExist("Target not found or does not belong to the specified customer.")
        # if ce_id is not None:
        #     if not target.metadata.ce.id == ce_id:
        #         raise DoesNotExist("Target not found or does not belong to the specified cloud enabler.")
        return cls.objects.get(id=_id).delete()

    @classmethod
    def get_target_by_subscribed_target_id(cls, target_id: int) -> Optional["Targets"]:
        """
        Retrieve target that have a subscribed target matching the given target Id

        Args:
            target_id (int): Id of the target on the subscribed location

        Returns:
            Optional[Targets]: The first matching target
        """
        try:
            target = Targets.objects.filter(subscribed_g8s__match={"id": target_id}).first()
            return target
        except cls.DoesNotExist:
            return DoesNotExist(f"Policy with id {target_id} is not found")


class SnapshotPolicy(EmbeddedDocument):
    """Class for snapshot policy configuration."""

    cooperative = BooleanField(help_text="Indicates if the snapshot policy is cooperative.")
    cooperative_timeout = IntField(help_text="The timeout for cooperative snapshots.")
    cooperative_failure_behaviour = StringField(
        choices=SnapshotPolicyFailureBehaviour, help_text="The behavior on cooperative snapshot failure."
    )
    timeout = IntField(help_text="The timeout for snapshots.")
    retry_pause = IntField(help_text="The pause between retries.")
    retry_times = IntField(help_text="The number of retry attempts.")


class Policies(Document):
    """Class representing a backup policy."""

    name = StringField(
        required=True, readOnly=True, export=True, editable=False, help_text="The name of the backup policy."
    )
    snapshot_policy = EmbeddedDocumentField(SnapshotPolicy, help_text="The snapshot policy configuration.")
    cron = StringField(required=True, editable=True, export=False, help_text="The cron schedule for the backup policy")
    restic_retention_flags = StringField(help_text="The retention flags for Restic backups.")
    failure_report_email = StringField(help_text="The email address for failure reports.")
    created_at = IntField(
        readOnly=True,
        editable=False,
        export=True,
        default=time.time,
        help_text="The timestamp when the backup policy was created.",
    )
    updated_at = IntField(help_text="The timestamp when the backup policy was last updated.")
    subscribed_g8s = EmbeddedDocumentListField(SubscribedLocation, help_text="List of subscribed g8s locations.")
    metadata = EmbeddedDocumentField(Metadata, help_text="Metadata associated with the backup policy.")

    @classmethod
    def get_by_id(
        cls,
        _id: str,
        customer_id: Optional[str] = None,
    ) -> Optional["Policies"]:
        """Retrieve a backup policy by its ID, optionally filtered by customer ID in metadata"""
        try:
            policy = cls.objects.get(id=_id)
            if customer_id is not None:
                if not policy.metadata.customer.id == customer_id:
                    raise DoesNotExist("Policy not found or does not belong to the specified customer.")
            # if ce_id is not None:
            #     if not (policy.metadata.ce.id == ce_id and policy.metadata.by_ce is True):
            #         raise DoesNotExist("Policy not found or does not belong tocecified cloud enabler.")
            return policy
        except cls.DoesNotExist:
            return DoesNotExist(f"Policy with id {_id} is not found")

    @classmethod
    def list(
        cls,
        target: Optional[str] = None,
        by_customer: Optional[bool] = None,
        customer_id: Optional[str] = None,
        by_vco_admin: Optional[bool] = None,
        vco_id: Optional[str] = None,
        by_ce: Optional[str] = None,
        ce_id: Optional[str] = None,
    ) -> Sequence["Policies"]:
        """List all backup policies, optionally filtered by target, customer, or VCO admin.

        Args:
            target (Optional[str]): The target to filter by.
            by_customer (Optional[bool]): Whether the policy is managed by the customer.
            customer_id (Optional[str]): The ID of the customer to filter by.
            by_vco_admin (Optional[bool]): Whether the policy is managed by the VCO admin.
            vco_id (Optional[str]): The ID of the VCO to filter by.

        Returns:
            Sequence[Policies]: A list of backup policy documents.
        """
        query = cls.objects()

        if target:
            query = query.filter(target=target)

        if by_customer is not None:
            query = query.filter(metadata__by_customer=by_customer)

        if customer_id:
            query = query.filter(metadata__customer__id=customer_id)

        if by_vco_admin is not None:
            query = query.filter(metadata__by_vco_admin=by_vco_admin)

        if vco_id:
            query = query.filter(metadata__vco__id=vco_id)

        if by_ce is True:
            query = query.filter(metadata__by_ce=by_ce)

        if ce_id:
            query = query.filter(metadata__ce__id=ce_id)

        return query

    @classmethod
    def delete_by_id(cls, _id: str, customer_id: Optional[str] = None) -> bool:
        """Delete a backup policy by its ID.

        Args:
            id (str): The ID of the backup policy to delete.

        Returns:
            bool: True if the deletion was successful, False otherwise.
        """
        policy = cls.objects.get(id=_id)
        if customer_id is not None:
            if not policy.metadata.customer.id == customer_id:
                raise DoesNotExist("Policy not found or does not belong to the specified customer.")
        # if ce_id is not None:
        #     if not policy.metadata.ce.id == ce_id:
        #         raise DoesNotExist("Policy not found or does not belong to the specified cloud enabler.")
        return cls.objects.get(id=_id).delete()

    @classmethod
    def get_policy_by_subscribed_policy_id(cls, policy_id: int, location: str) -> Optional["Policies"]:
        """
        Retrieve policy that have a subscribed policy matching the given policy Id

        Args:
            policy_id (int): Id of the policy on the subscribed location
            location (str): subscribed location

        Returns:
            Optional[Policies]: The first matching policy
        """
        try:
            policy = Policies.objects.filter(subscribed_g8s__match={"id": policy_id, "location": location}).first()
            return policy
        except cls.DoesNotExist:
            return DoesNotExist(f"Policy with id {policy_id} is not found")
