# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

from typing import Iterable

from mongoengine import BinaryField, Document, IntField, StringField


class DnsSigningKey(Document):
    """Contains information for dns zone signing keys for all customer and vco TLDs"""

    zone_name = StringField(required=True, help_text="Zone name")
    content = BinaryField(required=True, help_text="Keys content")
    file_name = StringField(help_text="File name")
    created_at = IntField(help_text="created at")

    @classmethod
    def list_keys(cls, zone_name: str) -> Iterable["DnsSigningKey"]:
        """List dns signing keys

        Args:
            zone_name (str): Zone name

        Returns:
            Iterable[DnsSigningKey]: List of dns signing keys
        """
        return cls.objects(zone_name=zone_name)

    @classmethod
    def delete_keys(cls, zone_name: str) -> None:
        """Delete dns signing keys

        Args:
            zone_name (str): Zone name
        """
        return cls.objects(zone_name=zone_name).delete()
