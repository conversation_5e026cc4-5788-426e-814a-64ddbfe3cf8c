# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, REPRODUC<PERSON>, <PERSON>ISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

import base64
from io import BytesIO

from gitlab.exceptions import GitlabGetError

from meneja.lib.giggitlab import GitLab
from meneja.structs.meneja.dataclasses.g8 import G8DocumentationStruct
from meneja.structs.meneja.dataclasses.knowledge_base import KnowledgeBaseCategoryTreeStruct, TopicStruct

gitlab = GitLab()

IGNORED_DIRECTORIES = ["images"]
IGNORED_FILES = ["README.md", ".gitkeep"]


def get_g8_knowledge_base_tree() -> list:
    """get knowledge base repo tree

    Returns:
        list[KnowledgeBaseCategoryTreeStruct]: tree structure
    """

    project = gitlab.get_knowledge_base_project()

    repo_tree = project.repository_tree(ref="main", recursive=True)

    categories = []
    for node in repo_tree:
        if node["type"] == "tree" and node["name"] not in IGNORED_DIRECTORIES:
            parent = node["path"].split("/")[0] if len(node["path"].split("/")) != 1 else None
            categories.append(KnowledgeBaseCategoryTreeStruct(node["name"], [], parent))

        if node["type"] == "blob" and node["name"] not in IGNORED_FILES:
            topic_category = node["path"].split("/")[-2]

            for category in categories:
                if topic_category == category.name:
                    category.children.append(TopicStruct(node["name"].split(".")[0], node["path"]))

    return categories


def get_knowledge_base_topic(path) -> G8DocumentationStruct:
    """get knowledge base topic

    Args:
        path (str): topic file path

    Raises:
        KeyError: no topic found in the given path

    Returns:
        str: topic content
    """
    project = gitlab.get_knowledge_base_project()
    try:
        file = project.files.get(path, ref="main")
    except GitlabGetError as e:
        if e.response_code == 404:
            raise KeyError("Topic was not found") from e
        raise e
    content = file.decode()
    return G8DocumentationStruct(content=content.decode("utf-8"))


def delete_knowledge_base_topic(path) -> None:
    """delete knowledge base topic

    Args:
        path (str): topic file path

    Raises:
        KeyError: no topic found in the given path

    Returns:
        None
    """

    project = gitlab.get_knowledge_base_project()
    try:
        project.files.delete(file_path=path, commit_message=f"Delete {path.split('/')[-1]}", branch="main")
    except GitlabGetError as e:
        if e.response_code == 404:
            raise KeyError("Topic was not found") from e
        raise e


def get_knowledge_base_image(image_name: str) -> BytesIO:
    """get knowledge base topic image

    Args:
        path (str): image file path

    Raises:
        KeyError: no image found in the given path

    Returns:
        BytesIO: Image content as a byte stream
    """

    project = gitlab.get_knowledge_base_project()
    try:
        file = project.files.get(f"images/{image_name}", ref="main")
    except GitlabGetError as e:
        if e.response_code == 404:
            raise KeyError("Image was not found") from e
        raise e
    file_content = base64.b64decode(file.content)

    return BytesIO(file_content)
