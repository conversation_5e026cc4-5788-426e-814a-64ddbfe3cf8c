# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, REPRODUC<PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

import logging
import time

from dynaqueue.scheduler import schedule
from mongoengine.errors import DoesNotExist

from meneja.business.g8 import get_g8_jwt_from_db, list_g8s
from meneja.jobs import job
from meneja.lib.clients.g8.client import G8ClientA<PERSON>V1
from meneja.lib.connection import DynaqueueConnection
from meneja.lib.utils import report_workflow_failure
from meneja.model.g8_power_archive import G8PowerStatus, NodeStatus, SwitchStatus
from meneja.structs.meneja.dataclasses.g8_node_block import (
    G8PowerStatusStruct,
    NodePowerStatusStruct,
    PowerStatus,
    SwitchPowerStatusStruct,
)

logger = logging.getLogger()
dynaqueue_client = DynaqueueConnection.get_client()


@schedule(cron="30 0 * * *", description="Fetch and archive daily G8s nodes/switches power status")
def g8s_power_daily_status() -> None:
    """Collect nodes/switches power status over last 24 h for all G8s"""

    for g8 in list_g8s(include_inactive=False):
        task = generate_g8_power_status_archive(g8_name=g8.name)
        logger.info("Task id: %s", task.task_id)


@job(title="G8 nodes/switches power status", retry_count=1, timeout=5 * 60, cleanup=report_workflow_failure)
def generate_g8_power_status_archive(g8_name: str) -> None:
    """Archive g8 nodes/switches power status on daily basis

    Args:
        g8_name (str): G8 name
    """
    logger.info("Collect power status for g8: %s", g8_name)
    statuses = G8ClientApiV1(g8_name, jwt=get_g8_jwt_from_db(g8_name)).nodes.get_g8_node_power_status()
    g8_power_status = None

    try:
        g8_power_status = G8PowerStatus.get(g8_name=g8_name)
    except DoesNotExist:
        g8_power_status = G8PowerStatus(g8_name=g8_name).save()

    for node in statuses.nodes:
        g8_power_status.nodes.append(
            NodeStatus(
                id=node.id,
                name=node.name,
                status=node.status,
                ping_status=node.ping_status,
                timestamp=int(time.time()),
            )
        )
    for switch in statuses.switches:
        g8_power_status.switches.append(
            SwitchStatus(
                name=switch.name,
                ping_status=switch.ping_status,
                timestamp=int(time.time()),
            )
        )
        g8_power_status.save()


def get_g8_power_status_history(g8_name: str = "", start: int = None, end: int = None) -> G8PowerStatusStruct:
    """Get G8 nodes/switches power status history

    Args:
        g8_name (str): Name of the G8 node
        start (int): Start timestamp
        end (int): End timestamp

    Returns:
        G8PowerStatusStruct: A list of the G8 power status history
    """

    if len(g8_name) == 0:
        raise ValueError("Invalid g8 name")

    if start and end:
        if start > end:
            raise ValueError(
                "Invalid time frame: The end timestamp must be greater than or equal to the start timestamp"
            )

    g8_power_archive = G8PowerStatus.aggregate(g8_name, start, end)
    output = G8PowerStatusStruct(nodes=[], switches=[])

    for node in g8_power_archive["nodes"]:
        ping_status = []
        statuses = []
        for status, percentage in node["status"].items():
            statuses.append(PowerStatus(status=status, percentage=percentage))

        for status, percentage in node["ping_status"].items():
            ping_status.append(PowerStatus(status=status, percentage=percentage))

        output.nodes.append(
            NodePowerStatusStruct(
                id=node["_id"],
                name=node["name"],
                status=statuses,
                ping_status=ping_status,
            )
        )
    for switch in g8_power_archive["switches"]:
        ping_status = []
        for status, percentage in switch["ping_status"].items():
            ping_status.append(PowerStatus(status=status, percentage=percentage))

        output.switches.append(
            SwitchPowerStatusStruct(
                name=switch["_id"],
                ping_status=ping_status,
            )
        )

    return output
