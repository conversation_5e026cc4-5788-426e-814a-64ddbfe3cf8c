# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, REPRODUC<PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@


from typing import List

from mongoengine import DoesNotExist

from meneja.lib.clients.g8.client import G8<PERSON>lientApiV1
from meneja.lib.clients.g8.lib.rest import ApiException
from meneja.lib.enumeration import VgpuStatus
from meneja.model.vco.customer import Customer
from meneja.structs.meneja.dataclasses.vgpuStruct import VGPUStruct


def list_active_vgpus(g8_name: str, jwt: str) -> List[VGPUStruct]:
    """List als active vGPUS on a specific g8

    Args:
        g8_name (str): g8 name
        jwt (str): jwt
    """
    g8_vgpus = []

    result = (
        G8ClientApiV1(g8_name, jwt=jwt)
        .vgpus.g8_list_vgpu(limit=0, start_after=0, status=[VgpuStatus.CREATED.value])
        .data
    )
    for vgpu in result:
        try:
            customer = Customer.get_by_location_account(g8_name, vgpu.account_id)
        except DoesNotExist:
            customer = {"customer_id": "", "vco": ""}

        g8_vgpus.append(
            VGPUStruct(
                account_id=vgpu.account_id,
                account_name=customer["customer_id"],
                vco=customer["vco"],
                vgpu_name=vgpu.name,
                gpu_id=vgpu.gpu_id,
                status=vgpu.status,
                description=vgpu.description,
                creation_time=vgpu.creation_time,
            )
        )
    return g8_vgpus


def update_gpu_config(g8_name: str, gpu_id: str, jwt: str, payload: dict) -> str:
    """Update GPU configurations

    Args:
        g8_name (str): Name of the g8
        gpu_id (str): GPU ID
        jwt (str): jwt
        payload (dict): GPU configuration data
    Return:
        str: gpu_id after update

    """
    api = G8ClientApiV1(g8_name, jwt=jwt)
    try:
        disabled = api.gpus.g8_disable_gpu(gpu_id=gpu_id)
        if not disabled:
            raise ValueError(f"GPU with id {gpu_id} was not found")
    except ApiException as err:
        if err.status == 400:
            raise ValueError("Can not edit GPU that has active attached vGPUs") from err
        raise err

    return {"gpu_id": api.gpus.g8_enable_gpu(payload).id}
