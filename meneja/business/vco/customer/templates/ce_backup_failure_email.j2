

<p>Dear {{ backup.metadata.ce.name }},</p>

<p>
  We regret to inform you that the backup for a virtual machine <strong>{{ backup.vm.name }}</strong> with ID (<strong>{{ backup.vm.id }}</strong>) has failed. This machine belongs to <strong>{{ backup.metadata.vco.name }}</strong>.
</p>

<h3>Details:</h3>
<ul>
  <li>
    <strong>Location:</strong>
    <a href="{{ base_url }}/g8s/{{ backup.location }}">{{ backup.location }}</a>
  </li>
  <li>
    <strong>Virtual machine:</strong> {{ backup.vm.name }}
  </li>
  <li>
    <strong>Backup:</strong> {{ backup.id }}
  </li>
  <li>
    <strong>Backup target:</strong>
    <a href="{{ base_url }}/admin/cloud-enablers/{{backup.metadata.ce.id}}/backups/targets/{{ backup.target }}">{{ target.name }}</a>
  </li>
  <li>
    <strong>Backup policy:</strong>
    <a href="{{ base_url }}/admin/cloud-enablers/{{backup.metadata.ce.id}}/backups/policies/{{ backup.policy }}">{{ backup.policy_name }}</a>
  </li>
  <li>
    <strong>Failure reason:</strong> {{ backup.failure_reason }}
  </li>
  <li>
    <strong>Failure details:</strong> {{ backup.failure_description }}
  </li>
</ul>

<p>
  Please take immediate action to investigate the issue and ensure your data is protected.
  You may check the backup logs or contact our support team for assistance.
</p>

<p>
  If you need any help, feel free to reach out to our support team at
  <a href="mailto:<EMAIL>"><EMAIL></a>.
</p>

<p><strong>Workflow ID:</strong> {{ backup.workflow }}</p>

<p>Best regards,<br>
Whitesky support team</p>
