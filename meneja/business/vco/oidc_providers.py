# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

"""
Business logic for interacting with IAM OIDC providers
"""

import requests
from flask import current_app
from flask_itsyouonline import get_current_user_info
from meneja.lib.enumeration import EnvironmentName
from meneja.model.vco import VCO


def get_iam_base_url(vco_id):
    """
    Get the base URL for the IAM service

    Args:
        vco_id (str): VCO ID

    Returns:
        str: Base URL for the IAM service
    """
    vco = VCO.get_by_id(vco_id, only=["iam_domain"])
    is_https = EnvironmentName.current() != EnvironmentName.TEST
    protocol = "https" if is_https else "http"
    return f"{protocol}://{vco.iam_domain}"


def list_oidc_providers(vco_id, only_active=False):
    """
    List OIDC providers from the IAM service

    Args:
        vco_id (str): VCO ID
        only_active (bool): Whether to only return active providers

    Returns:
        list: List of OIDC providers
    """
    base_url = get_iam_base_url(vco_id)
    jwt = get_current_user_info().jwt

    url = f"{base_url}/admin/oidc-providers"
    if only_active:
        url += "?active=true"

    response = requests.get(url, headers={"Authorization": f"bearer {jwt}"})

    if response.status_code != 200:
        current_app.logger.error(f"Failed to list OIDC providers: {response.text}")
        response.raise_for_status()

    return response.json().get("results", [])


def get_oidc_provider(vco_id, provider_id):
    """
    Get an OIDC provider by ID

    Args:
        vco_id (str): VCO ID
        provider_id (str): OIDC provider ID

    Returns:
        dict: OIDC provider details
    """
    base_url = get_iam_base_url(vco_id)
    jwt = get_current_user_info().jwt

    response = requests.get(
        f"{base_url}/admin/oidc-providers/{provider_id}", headers={"Authorization": f"bearer {jwt}"}
    )

    if response.status_code == 404:
        return None

    if response.status_code != 200:
        current_app.logger.error(f"Failed to get OIDC provider: {response.text}")
        response.raise_for_status()

    return response.json()


def create_oidc_provider(vco_id, provider_data):
    """
    Create a new OIDC provider

    Args:
        vco_id (str): VCO ID
        provider_data (dict): OIDC provider configuration

    Returns:
        dict: Created OIDC provider
    """
    base_url = get_iam_base_url(vco_id)
    jwt = get_current_user_info().jwt

    response = requests.post(
        f"{base_url}/admin/oidc-providers", headers={"Authorization": f"bearer {jwt}"}, json=provider_data
    )

    if response.status_code != 201:
        current_app.logger.error(f"Failed to create OIDC provider: {response.text}")
        response.raise_for_status()

    return response.json()


def update_oidc_provider(vco_id, provider_id, provider_data):
    """
    Update an existing OIDC provider

    Args:
        vco_id (str): VCO ID
        provider_id (str): OIDC provider ID
        provider_data (dict): Updated OIDC provider configuration

    Returns:
        dict: Updated OIDC provider
    """
    base_url = get_iam_base_url(vco_id)
    jwt = get_current_user_info().jwt

    # Ensure the ID is set in the provider data
    provider_data["id"] = provider_id

    response = requests.put(
        f"{base_url}/admin/oidc-providers/{provider_id}", headers={"Authorization": f"bearer {jwt}"}, json=provider_data
    )

    if response.status_code != 200:
        current_app.logger.error(f"Failed to update OIDC provider: {response.text}")
        response.raise_for_status()

    return response.json()


def delete_oidc_provider(vco_id, provider_id):
    """
    Delete an OIDC provider

    Args:
        vco_id (str): VCO ID
        provider_id (str): OIDC provider ID

    Returns:
        bool: True if successful
    """
    base_url = get_iam_base_url(vco_id)
    jwt = get_current_user_info().jwt

    response = requests.delete(
        f"{base_url}/admin/oidc-providers/{provider_id}", headers={"Authorization": f"bearer {jwt}"}
    )

    if response.status_code != 204:
        current_app.logger.error(f"Failed to delete OIDC provider: {response.text}")
        response.raise_for_status()

    return True


def activate_oidc_provider(vco_id, provider_id):
    """
    Activate an OIDC provider

    Args:
        vco_id (str): VCO ID
        provider_id (str): OIDC provider ID

    Returns:
        bool: True if successful
    """
    base_url = get_iam_base_url(vco_id)
    jwt = get_current_user_info().jwt

    response = requests.post(
        f"{base_url}/admin/oidc-providers/{provider_id}/activate", headers={"Authorization": f"bearer {jwt}"}
    )

    if response.status_code != 200:
        current_app.logger.error(f"Failed to activate OIDC provider: {response.text}")
        response.raise_for_status()

    return True


def deactivate_oidc_provider(vco_id, provider_id):
    """
    Deactivate an OIDC provider

    Args:
        vco_id (str): VCO ID
        provider_id (str): OIDC provider ID

    Returns:
        bool: True if successful
    """
    base_url = get_iam_base_url(vco_id)
    jwt = get_current_user_info().jwt

    response = requests.post(
        f"{base_url}/admin/oidc-providers/{provider_id}/deactivate", headers={"Authorization": f"bearer {jwt}"}
    )

    if response.status_code != 200:
        current_app.logger.error(f"Failed to deactivate OIDC provider: {response.text}")
        response.raise_for_status()

    return True


def get_password_login_status(vco_id):
    """
    Get the current status of password login

    Args:
        vco_id (str): VCO ID

    Returns:
        dict: Password login status
    """
    base_url = get_iam_base_url(vco_id)
    jwt = get_current_user_info().jwt

    response = requests.get(f"{base_url}/password-login/status", headers={"Authorization": f"bearer {jwt}"})

    if response.status_code != 200:
        current_app.logger.error(f"Failed to get password login status: {response.text}")
        response.raise_for_status()

    return response.json()


def enable_password_login(vco_id):
    """
    Enable password-based login

    Args:
        vco_id (str): VCO ID

    Returns:
        bool: True if successful
    """
    base_url = get_iam_base_url(vco_id)
    jwt = get_current_user_info().jwt

    response = requests.post(f"{base_url}/admin/password-login/enable", headers={"Authorization": f"bearer {jwt}"})

    if response.status_code != 200:
        current_app.logger.error(f"Failed to enable password login: {response.text}")
        response.raise_for_status()

    return True


def disable_password_login(vco_id):
    """
    Disable password-based login

    Args:
        vco_id (str): VCO ID

    Returns:
        bool: True if successful
    """
    base_url = get_iam_base_url(vco_id)
    jwt = get_current_user_info().jwt

    response = requests.post(f"{base_url}/admin/password-login/disable", headers={"Authorization": f"bearer {jwt}"})

    if response.status_code != 200:
        current_app.logger.error(f"Failed to disable password login: {response.text}")
        response.raise_for_status()

    return True
