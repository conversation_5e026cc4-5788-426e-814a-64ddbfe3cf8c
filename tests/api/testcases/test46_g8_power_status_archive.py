# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, REPRODUC<PERSON>, DIS<PERSON>OSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

# pylint: disable=no-member

import time

from pytest import mark

from meneja.business.g8.g8_node_status_archive import generate_g8_power_status_archive
from meneja.lib.connection import MongoConnection
from meneja.model.g8_power_archive import G8PowerStatus
from tests.api.testcases import MenejaApiBaseTestCase

MongoConnection.get_client()


class TestG8PowerStatusArchive(MenejaApiBaseTestCase):
    """Test G8 nodes power status archive"""

    @mark.nightly
    def test_get_current_g8_capacity(self):
        """Test for getting g8 nodes/switches power status archive"""

        G8PowerStatus.drop_collection()
        empty_archive = self.api.g8s.getG8NodesPowerStatusHistory(g8_name=self.TEST_G8)
        self.assertListEqual(empty_archive["nodes"], [])
        self.assertListEqual(empty_archive["switches"], [])

        self.logger.info("Collect G8 nodes/switches power status archive")
        nr_samples = 5
        for _ in range(nr_samples):
            generate_g8_power_status_archive(g8_name=self.TEST_G8)
            time.sleep(20)

        response = self.api.g8s.getG8NodesPowerStatusHistory(g8_name=self.TEST_G8)
        self.assertTrue(len(response["nodes"]) > 0 and len(response["switches"]) > 0)
        for node in response["nodes"]:
            total_node_status_percentage = sum([x["percentage"] for x in node["status"]])
            total_node_ping_status_percentage = sum([x["percentage"] for x in node["ping_status"]])
            self.assertAlmostEqual(100, total_node_status_percentage)
            self.assertAlmostEqual(100, total_node_ping_status_percentage)

        for switch in response["switches"]:
            total_switch_ping_status_percentage = sum([x["percentage"] for x in switch["ping_status"]])
            self.assertAlmostEqual(100, total_switch_ping_status_percentage)
