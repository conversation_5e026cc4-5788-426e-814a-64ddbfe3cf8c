# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, REP<PERSON>DUC<PERSON>, <PERSON><PERSON>CLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
# pylint: disable=no-member

import logging
import os

from faker import Faker
from pytest import mark

from meneja.business.g8.g8_api import G8Client
from tests.api.testcases import VCOApiBaseTestCase

READ_CHUNK_SIZE = 2 * 1024**2
MAIN_PATH = "C:\\" + "cairo-cloud.eg.local" + "\\octopus-license-scanner\\"
SCRIPT_PATH = MAIN_PATH + "octopus.ps1"
AGENT_PATH = MAIN_PATH + "OC-agent.cfg"

logging.basicConfig(level=logging.INFO)
logging.getLogger("faker").setLevel(logging.ERROR)
fake = Faker()


class VCOVirtualmachinesTests(VCOApiBaseTestCase):
    """VCO virtual machine tests class"""

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls._set_up_customer()
        cls.bucket_name = "vms-apis-testing"
        cls.logger.info("Allocating resources for VCO-Virtualmachines test")
        cls.cloudspace_id = cls._create_cloudspace()["cloudspace_id"]
        cls.external_cloudspace_id = cls._create_cloudspace()["cloudspace_id"]

    @classmethod
    def _minio_cleanup(cls):
        if not cls.minio_client.bucket_exists(cls.bucket_name):
            return
        for bucket_object in cls.minio_client.list_objects(cls.bucket_name):
            deletion_error = cls.minio_client.remove_object(cls.bucket_name, bucket_object.object_name)
            if deletion_error:
                raise deletion_error
        cls.minio_client.remove_bucket(cls.bucket_name)

    def setUp(self):
        self.vm_id = self._create_vm(
            self.customer_admin_api,
            self.cloudspace_id,
            wait_for_agent=True,
            image_id=os.environ.get("WINDOWS_IMAGE", "104"),
        )["vm_id"]

    @classmethod
    def tearDownClass(cls):
        """Tear down for VM tests class"""
        cls._minio_cleanup()
        super().tearDownClass()

    def tearDown(self) -> None:
        g8_client = G8Client(self.location, jwt=self.customer_admin_api.iyo_client.jwt)
        g8_client.delete_vm(self.vm_id, permanently=True)

    @mark.nightly
    def test01_virtual_machine_management(self):
        """Test VM management"""
        self.logger.info("Testing GET virtual machine")
        self.assertRaisesBadRequest(
            self.customer_admin_api.customers.readFile,
            customer_id=self.customer_id,
            cloudspace_id=self.cloudspace_id,
            vm_id=self.vm_id,
            filepath=SCRIPT_PATH,
            size=READ_CHUNK_SIZE,
        )
        self.assertRaisesBadRequest(
            self.customer_admin_api.customers.readFile,
            customer_id=self.customer_id,
            cloudspace_id=self.cloudspace_id,
            vm_id=self.vm_id,
            filepath=AGENT_PATH,
            size=READ_CHUNK_SIZE,
        )
        self.vco_admin_iam_client.complianceScan(customer_id=self.customer_id, force=True, vm_id=self.vm_id)
        self.customer_admin_api.customers.readFile(
            customer_id=self.customer_id,
            cloudspace_id=self.cloudspace_id,
            vm_id=self.vm_id,
            filepath=SCRIPT_PATH,
            size=READ_CHUNK_SIZE,
        )
        self.customer_admin_api.customers.readFile(
            customer_id=self.customer_id,
            cloudspace_id=self.cloudspace_id,
            vm_id=self.vm_id,
            filepath=AGENT_PATH,
            size=READ_CHUNK_SIZE,
        )
