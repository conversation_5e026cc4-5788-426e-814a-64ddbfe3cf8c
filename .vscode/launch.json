{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "VCO Backend Server",
            "type": "debugpy",
            "justMyCode": false,
            "request": "launch",
            "module": "meneja.server",
            "args": [
                "0.0.0.0",
                "8081",
                "https://git.gig.tech",
                "/isotemplate"
            ],
            "gevent": true,
            "env": {
                "SMTP_SERVER": "smtp.sendgrid.net",
                "SENDGRID_API_KEY": "*********************************************************************",
                "SUPPORT_EMAIL_ADDRESS": "<EMAIL>",
                "MENEJA_EMAIL_PASSWORD": "*********************************************************************",
                "MENEJA_EMAIL_LOGIN": "apikey",
                "SMTP_PORT": "587",
                "MNJ_FLASK_SECRET": "fEEuXYjkhVFm3YzviRYM6DjIlr7AATu0",
                "TLGM_CHANNEL": "-1001156056564",
                "TLGM_GROUP": "-388368748",
                "TLGM_NOTIFY": "1",
                "TLGM_UPDATE": "1",
                "TLGM_TOKEN": "u-ELhv51SWAM2q21LZG7NAt-2V2oGzZ2oWAbRdz_ZvFRATJgpvq5",
                "REQUESTS_CA_BUNDLE": "/etc/ssl/certs",
                "VCO": "yes",
                "GOOGLE_API_KEY": "AIzaSyCzeXCByJTIHlRSsseqI0UnpVn8e2dXPoM",
                "GIG_NS": "ns01.gig.tech,ns02.gig.tech,ns03.gig.tech",
                "ROCKET_CHANNEL": "dev-meneja-channel",
                "ROCKET_BOT_NAME": "<EMAIL>",
                "ROCKET_BOT_PASSWORD": "iLFn2gJCpNfBNaX",
                "ROCKET_SERVER_URL": "https://chat.gig.tech",
                "EXCHANGE_API_KEY": "cfd8797cbdb8a232bdd5cea4",
                "RANCHER_IMAGE_TAG": "image:6246b675396dbe0001ed5b2f",
                "UBUNTU_IMAGE_TAG": "image:602e5ee85212390001b5c765",
                "DEV_ENV_VPN_IP_ADDRESS": "*************",
                "ACRONIS_BACKUP_CDROM_ID": "610a908a0e847000014772c7",
                "VEEAM_BACKUP_CDROM_ID": "641afecde942ce0001822096",
                "MINIO_READ_ONLY_KEY": "TASIAIJCCECVZDPRNLIJ",
                "MINIO_READ_ONLY_SECRET": "DvcejFIc39Za5k/17JoPWzEfIwrAWo/iS7iQdfeV",
                "MINIO_READ_ONLY_URL": "https://storage-qa-meneja.gig.tech",
            },
            "preLaunchTask": "patch-debugger",
        },
        {
            "name": "Meneja Server",
            "type": "debugpy",
            "justMyCode": false,
            "request": "launch",
            "module": "meneja.server",
            "args": [
                "0.0.0.0",
                "8080",
                "http://meneja-dev.gig.tech.local/callback",
                "${env:MNJ_IAM_ORGANIZATION}",
                "${env:MNJ_SECRET}",
                "https://git.gig.tech",
                "/isotemplate"
            ],
            "gevent": true,
            "env": {
                "MENEJA": "yes",
                "SMTP_SERVER": "smtp.sendgrid.net",
                "SENDGRID_API_KEY": "*********************************************************************",
                "SUPPORT_EMAIL_ADDRESS": "<EMAIL>",
                "MENEJA_EMAIL_PASSWORD": "*********************************************************************",
                "MENEJA_EMAIL_LOGIN": "apikey",
                "SMTP_PORT": "587",
                "MNJ_FLASK_SECRET": "/+KSwBtIksqPGeC79lC52D8GBjfJI4PU",
                "TLGM_CHANNEL": "-1001156056564",
                "TLGM_GROUP": "-388368748",
                "TLGM_NOTIFY": "1",
                "TLGM_UPDATE": "1",
                "TLGM_TOKEN": "778269905:AAGHu3N9f7tcrg6P2VkMDR9jTpFYbgFMHD4",
                "GOOGLE_API_KEY": "AIzaSyCzeXCByJTIHlRSsseqI0UnpVn8e2dXPoM",
                "G8_ADMIN_API_DEFINITIONS_URL": "https://qt-gen-dc01-001.gig.tech/api/1/swagger.json",
                "GIG_NS": "ns01.gig.tech,ns02.gig.tech,ns03.gig.tech",
                "ROCKET_CHANNEL": "dev-meneja-channel",
                "ROCKET_BOT_NAME": "<EMAIL>",
                "ROCKET_BOT_PASSWORD": "iLFn2gJCpNfBNaX",
                "ROCKET_SERVER_URL": "https://chat.gig.tech",
                "EXCHANGE_API_KEY": "cfd8797cbdb8a232bdd5cea4",
                "TWILIO_ACCOUNT_SID": "",
                "TWILIO_AUTH_TOKEN": "",
                "TWILIO_PHONE_NUMBER": "",
                "MNJ_TOKEN": "eyJhbGciOiJFUzM4NCIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************.v_2OD4o5alPm3PEQJhAyvnrF9E3y4pOw2NNaH1YQ6D2pJB8mpzHEbE15Mhw4gqq1lfPkwWSVSlPmvePGueTzQ-B4jJKGi49-UF_Q8nZ9ji2nFMYJk1MJHXleSJfs5nvm",
            },
            "preLaunchTask": "patch-debugger",
        },
        {
            "name": "Dynaqueue Server",
            "type": "debugpy",
            "request": "launch",
            "module": "dynaqueue.server",
            "gevent": true,
            "args": [
                "--event-namespace",
                "/channel",
                "--redis-host",
                "mnj-redis"
            ],
            "preLaunchTask": "patch-debugger",
        },
        {
            "name": "Dynaqueue Generic worker",
            "type": "debugpy",
            "request": "launch",
            "module": "dynaqueue.worker",
            "args": [
                "--prometheus",
                "127.0.0.1:9090",
                "--context",
                "meneja.business.dynaqueue_worker_context",
                "--pool-size",
                "10",
                "--event-namespace",
                "/channel",
                "--redis-host",
                "mnj-redis"
            ],
            "gevent": true,
            "justMyCode": false,
            "env": {
                "SENDGRID_API_KEY": "*********************************************************************",
                "TLGM_CHANNEL": "-1001156056564",
                "TLGM_GROUP": "-388368748",
                "TLGM_NOTIFY": "1",
                "TLGM_UPDATE": "1",
                "TLGM_TOKEN": "778269905:AAGHu3N9f7tcrg6P2VkMDR9jTpFYbgFMHD4",
                "OCTOPUS_API_BASE_URL": "https://octopus.gig.tech/api",
                "OCTOPUS_TOKEN": "Hy3VYZtUx-zjal2DVd-gl0qnsHdFHWvpCyDpPrLqXnAdIBuMGo-FpFsUxHlbzDE2",
                "ROCKET_CHANNEL": "dev-meneja-channel",
                "ROCKET_BOT_NAME": "<EMAIL>",
                "ROCKET_BOT_PASSWORD": "iLFn2gJCpNfBNaX",
                "ROCKET_SERVER_URL": "https://chat.gig.tech",
                "RANCHER_IMAGE_TAG": "image:6246b675396dbe0001ed5b2f",
                "VCO_ID": "cairo-cloud.eg.local",
                "EXCHANGE_API_KEY": "cfd8797cbdb8a232bdd5cea4",
                "DEV_ENV_VPN_IP_ADDRESS": "*************",
            },
            "preLaunchTask": "patch-debugger",
        },
        {
            "name": "Python: Current File",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal",
            "env": {
                "OCTOPUS_API_BASE_URL": "https://octopus.gig.tech/api",
                "OCTOPUS_TOKEN": "Hy3VYZtUx-zjal2DVd-gl0qnsHdFHWvpCyDpPrLqXnAdIBuMGo-FpFsUxHlbzDE2"
            }
        },
        {
            "name": "Generate structs",
            "type": "debugpy",
            "request": "launch",
            "module": "tools.structs",
            "gevent": true
        },
        {
            "name": "Generate legacy G8 APIs",
            "type": "debugpy",
            "request": "launch",
            "module": "tools.legacy_g8_client",
        },
        {
            "name": "Generate new G8 api",
            "type": "debugpy",
            "request": "launch",
            "module": "meneja.tools.g8_client",
            "gevent": true
        },
        {
            "name": "Format codebase with black",
            "type": "node",
            "request": "launch",
            "runtimeExecutable": "black",
            "args": [
                ".",
                "--config=pyproject.toml"
            ]
        },
        {
            "name": "Test new G8 client",
            "type": "debugpy",
            "request": "launch",
            "module": "scripts.test_g8_client",
            "gevent": true
        },
        {
            "name": "Dynaqueue Belgium worker",
            "type": "debugpy",
            "request": "launch",
            "module": "dynaqueue.worker",
            "args": [
                "--queue",
                "belgium",
                "--prometheus",
                "127.0.0.1:9091",
                "--context",
                "meneja.business.dynaqueue_worker_context",
                "--pool-size",
                "1",
                "--redis-host",
                "mnj-redis"
            ],
            "gevent": true,
            "env": {
                "TLGM_CHANNEL": "-1001156056564",
                "TLGM_GROUP": "-388368748",
                "TLGM_NOTIFY": "1",
                "TLGM_UPDATE": "1",
                "TLGM_TOKEN": "778269905:AAGHu3N9f7tcrg6P2VkMDR9jTpFYbgFMHD4",
                "ROCKET_CHANNEL": "dev-meneja-channel",
                "ROCKET_BOT_NAME": "<EMAIL>",
                "ROCKET_BOT_PASSWORD": "iLFn2gJCpNfBNaX",
                "ROCKET_SERVER_URL": "https://chat.gig.tech"
            }
        },
        {
            "name": "Dynaqueue Swiss worker",
            "type": "debugpy",
            "request": "launch",
            "module": "dynaqueue.worker",
            "args": [
                "--queue",
                "swiss",
                "--prometheus",
                "127.0.0.1:9092",
                "--context",
                "meneja.business.dynaqueue_worker_context",
                "--pool-size",
                "1",
                "--redis-host",
                "mnj-redis"
            ],
            "gevent": true,
            "env": {
                "TLGM_CHANNEL": "-1001156056564",
                "TLGM_GROUP": "-388368748",
                "TLGM_NOTIFY": "1",
                "TLGM_UPDATE": "1",
                "TLGM_TOKEN": "778269905:AAGHu3N9f7tcrg6P2VkMDR9jTpFYbgFMHD4",
                "ROCKET_CHANNEL": "dev-meneja-channel",
                "ROCKET_BOT_NAME": "<EMAIL>",
                "ROCKET_BOT_PASSWORD": "iLFn2gJCpNfBNaX",
                "ROCKET_SERVER_URL": "https://chat.gig.tech"
            }
        },
        {
            "name": "Dynaqueue Austria worker",
            "type": "debugpy",
            "request": "launch",
            "module": "dynaqueue.worker",
            "args": [
                "--queue",
                "austria",
                "--prometheus",
                "127.0.0.1:9093",
                "--context",
                "meneja.business.dynaqueue_worker_context",
                "--pool-size",
                "1",
                "--redis-host",
                "mnj-redis"
            ],
            "gevent": true,
            "env": {
                "TLGM_CHANNEL": "-1001156056564",
                "TLGM_GROUP": "-388368748",
                "TLGM_NOTIFY": "1",
                "TLGM_UPDATE": "1",
                "TLGM_TOKEN": "778269905:AAGHu3N9f7tcrg6P2VkMDR9jTpFYbgFMHD4",
                "ROCKET_CHANNEL": "dev-meneja-channel",
                "ROCKET_BOT_NAME": "<EMAIL>",
                "ROCKET_BOT_PASSWORD": "iLFn2gJCpNfBNaX",
                "ROCKET_SERVER_URL": "https://chat.gig.tech"
            }
        },
        {
            "name": "Dynaqueue Imagebuilder worker",
            "type": "debugpy",
            "request": "launch",
            "module": "dynaqueue.worker",
            "args": [
                "--queue",
                "imagebuilder",
                "--context",
                "meneja.business.dynaqueue_worker_context",
                "--prometheus",
                "127.0.0.1:9094",
                "--redis-host",
                "mnj-redis"
            ],
            "gevent": true,
            "env": {
                "TLGM_CHANNEL": "-1001156056564",
                "TLGM_GROUP": "-388368748",
                "TLGM_NOTIFY": "1",
                "TLGM_UPDATE": "1",
                "TLGM_TOKEN": "778269905:AAGHu3N9f7tcrg6P2VkMDR9jTpFYbgFMHD4",
                "ROCKET_CHANNEL": "dev-meneja-channel",
                "ROCKET_BOT_NAME": "<EMAIL>",
                "ROCKET_BOT_PASSWORD": "iLFn2gJCpNfBNaX",
                "ROCKET_SERVER_URL": "https://chat.gig.tech"
            }
        },
        {
            "name": "VCO Backend Server with Docs",
            "type": "debugpy",
            "justMyCode": false,
            "request": "launch",
            "module": "meneja.server",
            "args": [
                "0.0.0.0",
                "8082",
                "https://git.gig.tech",
                "/isotemplate"
            ],
            "gevent": true,
            "env": {
                "VCO_DOCS": "yes",
                "MNJ_FLASK_SECRET": "fEEuXYjkhVFm3YzviRYM6DjIlr7AATu0",
                "TLGM_CHANNEL": "-1001156056564",
                "TLGM_GROUP": "-388368748",
                "TLGM_NOTIFY": "1",
                "TLGM_UPDATE": "1",
                "TLGM_TOKEN": "u-ELhv51SWAM2q21LZG7NAt-2V2oGzZ2oWAbRdz_ZvFRATJgpvq5",
                "REQUESTS_CA_BUNDLE": "/etc/ssl/certs",
                "VCO": "yes",
                "GOOGLE_API_KEY": "AIzaSyCzeXCByJTIHlRSsseqI0UnpVn8e2dXPoM",
                "GIG_NS": "ns01.gig.tech,ns02.gig.tech,ns03.gig.tech",
                "ROCKET_CHANNEL": "dev-meneja-channel",
                "ROCKET_BOT_NAME": "<EMAIL>",
                "ROCKET_BOT_PASSWORD": "iLFn2gJCpNfBNaX",
                "ROCKET_SERVER_URL": "https://chat.gig.tech",
                "EXCHANGE_API_KEY": "cfd8797cbdb8a232bdd5cea4",
                "RANCHER_IMAGE_TAG": "image:6246b675396dbe0001ed5b2f",
                "UBUNTU_IMAGE_TAG": "image:602e5ee85212390001b5c765",
            },
            "preLaunchTask": "build-docs",
        },
        {
            "name": "Dynaqueue Scheduler",
            "type": "debugpy",
            "request": "launch",
            "module": "dynaqueue.scheduler",
            "gevent": true,
            "args": [
                "--library",
                "meneja",
                "--prometheus",
                "127.0.0.1:9095",
                "--redis-host",
                "mnj-redis"
            ],
        },
        {
            "name": "Linting",
            "type": "debugpy",
            "request": "launch",
            "module": "nose",
            "console": "integratedTerminal",
            "gevent": true,
            "env": {
                "TEST_G8_NAME": "be-g8-3",
                "TESTING_G8": "be-g8-3",
                "TEST_IMAGE_ID": "10",
                "GEVENT_SUPPORT": "True"
            },
            "args": [
                "-m",
                "flake8",
                "--ignore",
                "E203,W503"
            ]
        },
        {
            "name": "Portal Pod",
            "type": "debugpy",
            "request": "attach",
            "connect": {
                "port": 5678,
                "host": "localhost"
            },
            "justMyCode": false,
            "pathMappings": [
                {
                    "localRoot": "/opt/code/git.gig.tech/gig-meneja/meneja/meneja",
                    "remoteRoot": "/usr/local/lib/python3.8/site-packages/meneja/"
                },
            ]
        },
        {
            "name": "Qa tests",
            "type": "debugpy",
            "request": "launch",
            "module": "nose",
            "console": "integratedTerminal",
            "gevent": true,
            "env": {
                "QA_TESTING_CUSTOMER_ID": "qa_testing_1",
                "QA_TESTING_VCO_DOMAIN": "console.cloudbuilders.be",
                "QA_TESTING_IAM_DOMAIN": "iam.cloudbuilders.be",
                "QA_TESTING_API_APP_ID": "#######",
                "QA_TESTING_API_APP_SECRET": "#######",
                "GEVENT_SUPPORT": "True"
            },
            "args": [
                "tests/api/qas_testcases/",
                "-sv",
                "--attr",
                "nightly=True",
            ]
        },
    ],
    "compounds": [
        {
            "name": "Compound",
            "configurations": [
                "VCO Backend Server",
                "Meneja Server",
                "Dynaqueue Server",
                "Dynaqueue Generic worker"
            ]
        }
    ]
}