{
    "python.linting.enabled": true,
    "editor.formatOnSave": true,
    "python.formatting.provider": "none",
    "python.formatting.blackArgs": [],
    "isort.args": [
        "--profile",
        "black",
        "--line-length",
        "120",
        "--wrap-length",
        "120",
        "--multi-line",
        "3",
        "--project",
        "meneja.*"
    ],
    "[python]": {
        "editor.defaultFormatter": "ms-python.black-formatter",
        "editor.codeActionsOnSave": {
            "source.organizeImports": "explicit"
        }
    },
    "flake8.args": [
        "--max-line-length=120",
        "--ignore=E203,W503,Q000,C812",
    ],
    "pylint.args": [
        "--max-line-length=120",
        "--disable=C0114,C0302,W0511",
    ],
    "cSpell.diagnosticLevel": "Hint",
    "cSpell.words": [
        "argparser",
        "autopep",
        "bson",
        "cdrom",
        "cdroms",
        "cloudenabler",
        "cloudenabler_id",
        "cloudenablers",
        "cloudspace",
        "cloudspaces",
        "datacenter",
        "datacenters",
        "decode",
        "decoded",
        "dynaqueue",
        "encode",
        "fget",
        "fput",
        "frombuffer",
        "gaierror",
        "Geocoded",
        "gevent",
        "globalid",
        "guids",
        "healthchecks",
        "id",
        "imdecode",
        "imencode",
        "IMREAD",
        "itsyouonline",
        "jsonify",
        "letsencrypt",
        "location",
        "memberof",
        "meneja",
        "minio",
        "Minio",
        "miniocl",
        "mongoengine",
        "nics",
        "novnc",
        "NVME",
        "objectspace",
        "objectspaces",
        "pcnet",
        "portforward",
        "portforwards",
        "pylint",
        "pytz",
        "reqparse",
        "restfull",
        "restx",
        "reverseproxy",
        "rocketchat",
        "serverpool",
        "Spendings",
        "Spla",
        "struct",
        "suborganization",
        "taskguid",
        "timeseries",
        "timezonefinder",
        "tldextract",
        "tobytes",
        "traefik",
        "vacuumer",
        "validate",
        "vcos",
        "vcpu",
        "vdisk",
        "virtio",
        "websockify",
        "werkzeug",
        "Whitesky",
        "yapf"
    ],
    "python.testing.pytestArgs": [
        "--tc-file=tests/config.ini",
        "tests",
    ],
    "python.testing.unittestEnabled": false,
    "python.testing.pytestEnabled": true,
}